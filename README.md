# aero-metadata-client

To run this application:

- Copy `env.example` to `.env.local`
- Ensure your backend is correctly setup as described in `aero-metadata-api/README.md`
- Run `yarn` to install dependencies
- Run `yarn dev` to start the application
- Voila! You can access the application in your browser at `localhost:4001`

Now we need to use Valet to proxy a proper domain to the localhost URL.

```
valet --secure proxy local.aero-metadata-api.test http://localhost:4001
```

And then you can access your portal at `https://local.aero-metadata-api.test`.

Once all is done and good, and your database has been seeded, log in with:

- Email: `<EMAIL>`
- Password: `password`
