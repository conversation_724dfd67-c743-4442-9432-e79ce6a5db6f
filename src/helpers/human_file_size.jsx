// Available units
const units = ['KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

const formatFileSize = (bytes) => {
  if (bytes === 0) return { size: 0, unit: 'B' }
  // Return empty object if bytes is null
  if (!bytes) return { size: null, unit: '-' }
  bytes = parseInt(bytes)
  // File size calculation method
  const thresh = 1024
  // Less than 1024 bytes should return bytes
  if (Math.abs(bytes) < thresh) return { size: bytes.toFixed(0), unit: 'B' }

  // Calc
  let u = -1
  do {
    bytes /= thresh
    ++u
  } while (Math.abs(bytes) >= thresh && u < units.length - 1)

  return { size: parseFloat(bytes.toFixed(2)), unit: units[u] }
}

const convertToBytes = (size, unit) => {
  const unitIndex = units.findIndex((opt) => opt === unit)
  // Convert to bytes: multiply by 1024 raised to the power of (unitIndex + 1)
  // +1 because KB (index 0) is 1024^1, MB (index 1) is 1024^2, etc.
  return size * Math.pow(1024, unitIndex + 1)
}

const humanFileSize = (bytes) => {
  const res = formatFileSize(bytes)
  if (res.size === null || res.size === 0) return ''
  return `${res.size} ${res.unit}`
}

export { humanFileSize, formatFileSize, convertToBytes, units }
