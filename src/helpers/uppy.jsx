import Uppy from '@uppy/core'
import XHRUpload from '@uppy/xhr-upload'
import AwsS3 from '@uppy/aws-s3'
import dayjs from 'dayjs'

export const startUppyUpload = (file, destinationURL, s3Path, updates) => {
  // Set up Uppy
  const uppy = new Uppy()

  uppyInstance = uppy

  const useMultipart = file.size > 100 * 2 ** 20

  if (useMultipart) {
    uppy.use(AwsS3, {
      method: 'PUT',
      shouldUseMultipart: true,
      companionUrl: `${process.env.VITE_API_URL}`,
    })
  } else {
    uppy.use(XHRUpload, {
      endpoint: destinationURL,
      method: 'PUT',
      formData: false,
      withCredentials: true,
    })
  }

  // @TODO: Maybe useful for multiple files / folders:
  // uppy.on('progress', (progress) => {
  //   // progress: integer (total progress percentage)
  //   console.log(progress)
  // })
  let lastUpdate = dayjs()

  uppy.on('upload-progress', (file, progress) => {
    if (dayjs().diff(lastUpdate, 'second') >= 1) {
      updates.onStatusChange('Running')
      updates.onProgress({ total_bytes: progress.bytesTotal, current_bytes: progress.bytesUploaded })
      lastUpdate = dayjs()
    }
  })
  uppy.on('upload-success', (file, response) => {
    updates.onStatusChange(null)
    updates.onComplete(file, destinationURL)
  })
  uppy.on('error', (error) => {
    updates.onError(error)
  })
  uppy.on('cancel-all', () => {
    updates.onStatusChange(null)
    updates.onProgress(null)
  })
  uppy.addFile({
    meta: {
      s3_path: s3Path,
    },
    name: file.name,
    data: file,
  })
  uppy.upload().then((result) => {
    if (result && result.failed.length > 0) {
      console.error('Errors:')
      result.failed.forEach((file) => {
        console.error(file.error)
      })
    }
  })
}

export const cancelUppy = () => {
  if (typeof uppyInstance.close() === 'function') {
    uppyInstance.close()
  }
}

var uppyInstance = null
