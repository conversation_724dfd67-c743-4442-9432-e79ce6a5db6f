import React from 'react'
import ReactDOM from 'react-dom/client'
import * as Sentry from '@sentry/react'
import axios from 'axios'
import App from './App.jsx'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import dayjs from 'dayjs'
import advancedFormat from 'dayjs/plugin/advancedFormat'
import dayjsPluginUTC from 'dayjs-plugin-utc'
import relativeTime from 'dayjs/plugin/relativeTime'
import timezone from 'dayjs/plugin/timezone'
import localizedFormat from 'dayjs/plugin/localizedFormat'
import './helpers/initialize_sentry'
import './styles/index.css'

dayjs.extend(relativeTime)
dayjs.extend(advancedFormat)
dayjs.extend(dayjsPluginUTC)
dayjs.extend(timezone)
dayjs.extend(localizedFormat)

console.log(`%c*************************`, `color: #fb0f3b; font-weight: bold; font-size: 24px;`)
console.log(`%c*  Powered by CineSend  *`, `color: #fb0f3b; font-weight: bold; font-size: 24px;`)
console.log(`%c*    Built in Canada    *`, `color: #fb0f3b; font-weight: bold; font-size: 24px;`)
console.log(`%c*************************`, `color: #fb0f3b; font-weight: bold; font-size: 24px;`)

// provide the default query function to your app with defaultOptions
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      queryFn: async ({ queryKey }) => {
        const { data } = await axios.get(`${queryKey.join('')}`)
        return data
      },
    },
  },
})

const queryParams = new URLSearchParams(window.location.search)
const searchToken = queryParams.get('token')
if (searchToken) {
  localStorage.setItem('user_token', searchToken)
}

// Default axios options:
axios.defaults.withCredentials = true
axios.defaults.withXSRFToken = true
axios.defaults.baseURL = import.meta.env.VITE_API_URL
axios.defaults.headers = {
  'X-Requested-With': 'XMLHttpRequest',
  Accept: 'application/json',
  // 'Content-Type': 'application/x-www-form-urlencoded'
}
axios.defaults.headers.common = { Authorization: `Bearer ${localStorage.getItem('user_token')}` }

const ErrorHandling = {
  // Callback called when an error is thrown and not caught by an ErrorBoundary.
  onUncaughtError: Sentry.reactErrorHandler((error, errorInfo) => {
    console.error('Uncaught error', error, errorInfo.componentStack)
  }),
  // Callback called when React catches an error in an ErrorBoundary.
  onCaughtError: Sentry.reactErrorHandler(),
  // Callback called when React automatically recovers from errors.
  onRecoverableError: Sentry.reactErrorHandler(),
}

ReactDOM.createRoot(document.getElementById('root'), ErrorHandling).render(
  <React.StrictMode>
    <QueryClientProvider client={queryClient}>
      <App />
    </QueryClientProvider>
  </React.StrictMode>
)
