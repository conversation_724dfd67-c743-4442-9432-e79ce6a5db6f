import { Status } from '@bitcine/cinesend-theme'
import { <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom'
import Helmet from 'react-helmet'
import { StoreProvider } from './contexts/store'
import Snackbar from './components/snackbar'
import Transfers from './components/transfers'
import Sidebar from './components/sidebar'
import { RoutesList } from './routes'
import useAuth from './hooks/use_auth'
import useUtilities from './hooks/use_utilities'

function AirlineCycleWrapper() {
  const { user, isLoading: isUserLoading, logOut } = useAuth()

  return (
    <>
      <Status pending={isUserLoading || logOut.isLoading} className='h-screen'>
        <div className='w-screen h-screen flex'>
          {user ? <Sidebar user={user} logOut={logOut} /> : null}
          <div className='w-full h-full overflow-auto ml-4 md:ml-0 pb-60'>
            <RoutesList isUserLoading={isUserLoading} user={user} logOut={logOut} />
          </div>
        </div>
      </Status>
      <BrandedHelmet organization={user?.organization} />
      <Snackbar />
      <Transfers />
    </>
  )
}

function App() {
  // This initializes some API calls that are used frequently.
  useUtilities()

  // this may be necessary if we host the portal on multiple domains and different clients have different logins.
  // const { data: organization, isLoading: isOrganizationLoading } = useQuery({
  //   queryKey: ['/domain'],
  //   refetchOnWindowFocus: false
  // })

  return (
    <BrowserRouter
      future={{
        v7_startTransition: true,
        // v7_relativeSplatPath: true // TODO: refactor in separate task
      }}
    >
      <StoreProvider>
        <AirlineCycleWrapper />
      </StoreProvider>
    </BrowserRouter>
  )
}

function BrandedHelmet({ organization }) {
  if (!organization) return null
  return (
    <Helmet>
      <meta charSet='utf-8' />
      <title>{organization.name}</title>
      {organization.favicon_url ? (
        <link rel='icon' href={organization.favicon_url} />
      ) : (
        <link rel='icon' href={'/public/favicon.png'} />
      )}
    </Helmet>
  )
}

export default App
