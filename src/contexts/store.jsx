import React from 'react'

const reducer = (state, action) => {
  switch (action.type) {
    case 'add_message':
      return {
        ...state,
        messages: [...state.messages, action.message],
      }
    case 'remove_message':
      return {
        ...state,
        messages: state.messages.filter((message) => message._id !== action.messageID),
      }
    case 'add_transfer':
      return {
        ...state,
        transfers: [...state.transfers, action.transfer],
      }
    case 'update_transfer':
      return {
        ...state,
        transfers: state.transfers.map((transfer) =>
          transfer.id === action.transfer.id ? { ...transfer, ...action.transfer } : transfer
        ),
      }
    case 'remove_transfer':
      return {
        ...state,
        transfers: state.transfers.filter((transfer) => transfer.id !== action.transferID),
      }
    default:
      return state
  }
}

const initialState = {
  messages: [],
  transfers: [],
}

export const StoreContext = React.createContext({
  state: initialState,
  dispatch: () => null,
})

export const StoreProvider = ({ children }) => {
  const [state, dispatch] = React.useReducer(reducer, initialState)

  return <StoreContext.Provider value={{ state, dispatch }}>{children}</StoreContext.Provider>
}
