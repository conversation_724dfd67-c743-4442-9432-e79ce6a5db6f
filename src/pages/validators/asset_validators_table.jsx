import React, { useState } from 'react'
import { Table } from '@bitcine/cinesend-theme/dist'
import { useQuery } from '@tanstack/react-query'

const AssetValidatorsTable = () => {
  const [perPage, setPerPage] = useState(10)
  const [page, setPage] = useState(0)
  const {
    data: validators,
    error,
    isLoading,
  } = useQuery([`validator-field-types?page=${page}&per_page=${perPage}&asset_validators=1`])

  return (
    <Table
      status={{
        pending: isLoading,
        error: error,
        errorMessage: error?.message,
      }}
      widths={['auto']}
      header={{
        columns: [{ text: 'Validator' }],
      }}
      body={{
        data: validators?.data,
        row: {
          compact: true,
          truncate: true,
          render: [
            (data) => (
              <p key={data.id}>
                <b>{data.name}</b>
                <br />
                {data.description}
              </p>
            ),
          ],
        },
        empty: {
          title: 'No validators found!',
          text: 'Create a new validator to begin.',
          icon: 'check-circle',
        },
      }}
      paginate={{
        currentPage: page,
        totalRows: validators?.meta.total,
        rowsPerPage: perPage,
        page: validators?.meta.current_page,
        onPageChange: (page) => setPage(page),
        onRowsPerPageChange: (perPage) => {
          setPerPage(perPage)
          setPage(0)
        },
      }}
    />
  )
}

export default AssetValidatorsTable
