import React, { useState } from 'react'
import useStore from '../../hooks/use_store'
import { useQuery } from '@tanstack/react-query'
import { Button, Chip, Table } from '@bitcine/cinesend-theme'
import EditModal from './edit_modal'

const FieldValidatorsTable = () => {
  const [perPage, setPerPage] = useState(10)
  const [page, setPage] = useState(0)
  const { data: validators, error, isLoading } = useQuery([`validator-field-types?page=${page}&per_page=${perPage}`])
  const [showModal, setShowModal] = useState(false)
  const [validator, setValidator] = useState({})
  const { createModel } = useStore()
  const fieldTypes = validators?.field_types || []

  const onToggleChange = (validatorId, fieldType, isEnabled) => {
    createModel.mutate({
      endpoint: 'validator-field-types',
      data: {
        validator_id: validatorId,
        field_type: fieldType,
        enabled: isEnabled,
      },
      onSuccess: () => {
        validators.data.find((v) => v.id === validatorId).field_types.find((ft) => ft.field_type === fieldType)
          ? (validators.data
              .find((v) => v.id === validatorId)
              .field_types.find((ft) => ft.field_type === fieldType).enabled = isEnabled)
          : validators.data
              .find((v) => v.id === validatorId)
              .field_types.push({ field_type: fieldType, enabled: isEnabled })
      },
    })
  }

  const openEditModal = (data) => {
    setValidator(data)
    setShowModal(true)
  }

  return (
    <>
      <Table
        status={{
          pending: isLoading,
          error: error,
          errorMessage: error?.message,
        }}
        widths={['360', 'auto', '75']}
        header={{
          columns: [{ text: 'Validator', key: 'validator' }, { text: 'Fields', key: 'fields' }, { text: 'Actions' }],
          sorting: {},
          filters: {},
        }}
        body={{
          data: validators?.data,
          row: {
            compact: true,
            spaced: true,
            truncate: true,
            render: [
              (data) => (
                <div key={data.id}>
                  <b>{data.name}</b>
                  <br />
                  {data.description}
                </div>
              ),
              (data) => (
                <div className='space-x-1 '>
                  {data.field_types.map(
                    (ft) => ft.enabled && <Chip label={ft.field_type} key={ft.field_type} size='small' />
                  )}
                </div>
              ),
              (data) => (
                <Button size='small' type='secondary' tertiary={true} onClick={() => openEditModal(data)}>
                  Edit
                </Button>
              ),
            ],
          },
          empty: {
            title: 'No validators found!',
            text: 'Create a new validator to begin.',
            icon: 'check-circle',
          },
        }}
        paginate={{
          currentPage: page,
          totalRows: validators?.meta.total,
          rowsPerPage: perPage,
          page: validators?.meta.current_page,
          onPageChange: (page) => setPage(page),
          onRowsPerPageChange: (perPage) => {
            setPerPage(perPage)
            setPage(0)
          },
        }}
      />
      {showModal && (
        <EditModal
          validator={validator}
          fieldTypes={fieldTypes}
          onClose={() => setShowModal(false)}
          onChange={(fieldType, enabled) => onToggleChange(validator.id, fieldType, enabled)}
        />
      )}
    </>
  )
}

export default FieldValidatorsTable
