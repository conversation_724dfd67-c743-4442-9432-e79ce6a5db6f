import { Checkbox, Modal } from '@bitcine/cinesend-theme/dist'

const EditModal = ({ validator, fieldTypes, onClose, onChange }) => {
  return (
    <Modal header={validator.name} onClose={onClose}>
      <p className='py-2'>{validator.description}</p>
      <p className='py-2'>Fields:</p>
      <ul className='space-y-2 '>
        {fieldTypes.map((field) => (
          <li key={field}>
            <Checkbox
              checked={validator.field_types.find((ft) => ft.field_type === field && ft.enabled === true) !== undefined}
              label={field}
              onChange={(e) => onChange(field, e.target.checked)}
            />
          </li>
        ))}
      </ul>
    </Modal>
  )
}

export default EditModal
