import Page from '../../components/layouts/page'
import CategoriesTable from '../../components/categories_table'
import { useNavigate, useParams } from 'react-router-dom'
import { Button, ButtonDropdown } from '@bitcine/cinesend-theme/dist'
import useStore from '../../hooks/use_store'
import axios from 'axios'
import openInNewTab from '../../helpers/openinnewtab'
import { useState } from 'react'
import ImportForm from '../../components/assets_table/import_form'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import useAuth from '../../hooks/use_auth'
import { modelTypes } from '../../constants/model_types'

const Categories = () => {
  const params = useParams()
  const navigate = useNavigate()
  const { createModel } = useStore()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const { data: airline } = useQuery(['airlines/', params.airlineID])
  const langID = airline?.languages?.[0]?.id || null
  const queryClient = useQueryClient()
  const queryKey = `/categories?cycleID=${params.cycleID}`
  const { canEditModel: canEditCategory } = useAuth(modelTypes.Category)

  const closeModal = () => {
    setIsModalOpen(false)
    queryClient.invalidateQueries([queryKey])
  }

  const exportCategories = ({ isTemplate }) => {
    createModel.mutate(
      {
        endpoint: 'exports',
        data: {
          name: `export-cycle-${params.cycleID}-type-category`,
          cycleID: params.cycleID,
          type: 'category',
          isTemplate,
          langID,
        },
      },
      {
        onSuccess: (res) => {
          if (res.data) {
            axios.get(`${process.env.VITE_API_URL}/exports/${res.data.export.id}/download`).then((r) => {
              openInNewTab(r.data.url)
            })
          }
        },
      }
    )
  }
  const categoryDropdownItems = [
    canEditCategory && {
      text: 'Create top-level category',
      icon: 'category',
      onClick: () =>
        createModel.mutate(
          {
            endpoint: 'categories',
            data: {
              title: 'New category list',
              type: 'categories',
              cycle_id: params.cycleID,
              is_root: true,
            },
          },
          { onSuccess: (res) => navigate(`${res.data.category.id}/items`) }
        ),
    },
    {
      text: 'Create category for assets',
      icon: 'list',
      onClick: () =>
        createModel.mutate(
          {
            endpoint: 'categories',
            data: { title: 'New category', type: 'assets', cycle_id: params.cycleID, is_root: false },
          },
          { onSuccess: (res) => navigate(`${res.data.category.id}/items`) }
        ),
    },
  ].filter((item) => item) // remove empty items;

  return (
    <Page
      title={'Categories'}
      buttons={[
        <div key={'asset-buttons'} className='space-x-2 flex items-center'>
          {categoryDropdownItems.length === 1 && (
            <Button icon={categoryDropdownItems[0].icon} onClick={categoryDropdownItems[0].onClick}>
              {categoryDropdownItems[0].text}
            </Button>
          )}
          {categoryDropdownItems.length > 1 && (
            <ButtonDropdown
              key='create-category'
              button={{
                text: 'Add Category',
                icon: 'add_circle_outline',
              }}
              dropdown={{
                content: categoryDropdownItems,
              }}
            />
          )}
          <ButtonDropdown
            button={{
              text: 'Export',
              icon: 'cloud_download',
            }}
            dropdown={{
              content: [
                {
                  text: 'Export Template',
                  onClick: () => exportCategories({ isTemplate: true }),
                },
                {
                  text: 'Export Populated',
                  onClick: () => exportCategories({ isTemplate: false }),
                },
              ],
            }}
          />
          <Button icon='cloud_upload' onClick={() => setIsModalOpen(true)}>
            import
          </Button>
        </div>,
      ]}
    >
      <CategoriesTable queryKey={queryKey} />
      {isModalOpen && (
        <ImportForm isOpen={isModalOpen} onClose={closeModal} cycleID={params.cycleID} assetType={'category'} />
      )}
    </Page>
  )
}

export default Categories
