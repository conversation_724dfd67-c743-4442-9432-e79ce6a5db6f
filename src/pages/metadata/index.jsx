import { Navigate, Route, Routes, useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import Asset from '../../components/asset'
import AssetsByType from '../../components/assets_table'
import useUtilities from '../../hooks/use_utilities'
import { Status } from '@bitcine/cinesend-theme/dist'

const Metadata = () => {
  const params = useParams()
  const { data: cycle, isLoading, error } = useQuery(['cycles/', params.cycleID])
  const { assetTypes: availableAssetTypes } = useUtilities()
  const { data: airline } = useQuery(['airlines/', params.airlineID])
  const enabledAssetTypes = availableAssetTypes?.filter((assetType) => airline?.asset_types?.includes(assetType.value))
  const topAssetTypes = availableAssetTypes
    ?.filter((assetType) => assetType.parent_type === null)
    .reduce((acc, assetType) => {
      acc[assetType.value] = assetType
      return acc
    }, {})
  if (enabledAssetTypes.length === 0) {
    return null
  }
  return (
    <Status pending={isLoading} error={error}>
      <Routes>
        {availableAssetTypes?.map((assetType) =>
          assetType.parent_type === null ? (
            <Route
              key={assetType.value}
              path={`/${assetType.route_value}`}
              element={<AssetsByType cycle={cycle} assetType={assetType} />}
            />
          ) : (
            <Route
              key={assetType.value}
              path={`/${topAssetTypes[assetType.parent_type].route_value}/:parentAssetID/${assetType.route_value}/:assetID/*`}
              element={<Asset cycle={cycle} />}
            />
          )
        )}

        <Route path='/:assetType/:assetID/*' element={<Asset cycle={cycle} />} />
        <Route path='*' element={<Navigate to={enabledAssetTypes[0].route_value} replace={true} />} />
      </Routes>
    </Status>
  )
}

export default Metadata
