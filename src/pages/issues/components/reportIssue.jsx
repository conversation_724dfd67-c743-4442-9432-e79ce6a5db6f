import React, { useState } from 'react'
import { Button, Input, Modal } from '@bitcine/cinesend-theme/dist'

export default function ReportIssue() {
  const [openIssueModal, setOpenIssueModal] = useState(false)

  const report = (payload) => (dispatch) =>
    dispatch({ type: 'REPORT_ISSUE', payload }).then(() => setOpenIssueModal(false))

  return (
    <div>
      <Button size='medium' type='primary' onClick={() => setOpenIssueModal(true)}>
        Report an issue
      </Button>
      {openIssueModal && (
        <Modal
          header='Report an Issue'
          confirmButton={{
            text: 'Submit',
            onClick: () => {
              report
            },
          }}
          onClose={() => setOpenIssueModal(null)}
        >
          <Input label='Describe the issue' placeholder='the issue...' />
        </Modal>
      )}
    </div>
  )
}
