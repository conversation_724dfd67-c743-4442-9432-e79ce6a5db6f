import Page from '../../components/layouts/page'
import { Button, Table, ButtonDropdown } from '@bitcine/cinesend-theme/dist'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import useTableFilters from '../../hooks/use_table_filters'
import useStore from '../../hooks/use_store'
import { useState } from 'react'
import CreateOrgModal from './create_org_modal'

const Organizations = () => {
  const { data: organizations, error, isLoading } = useQuery(['organizations'])
  const { queryString, searching } = useTableFilters({
    enableSearch: true,
  })

  const [createOrgModal, setCreateOrgModal] = useState(false)

  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { deleteModel } = useStore()

  return (
    <Page
      title='Organizations'
      buttons={[
        <Button key={'create_org_button'} onClick={() => setCreateOrgModal(true)}>
          Create Organization
        </Button>,
      ]}
    >
      <Table
        status={{
          pending: isLoading,
          error: error,
          errorMessage: error?.message,
        }}
        widths={['auto', 50]}
        header={{
          columns: [{ text: 'Name', key: 'name' }, { text: '' }],
          searching,
        }}
        body={{
          data: organizations,
          row: {
            onClick: (event, data) => navigate(`/settings/organizations/${data.id}`),
            render: [
              (data) => data.name,
              (data) => (
                <ButtonDropdown
                  kebab
                  dropdown={{
                    content: [
                      {
                        text: 'Delete Organization',
                        className: 'text-red-500',
                        icon: 'delete',
                        onClick: () =>
                          deleteModel.mutate({
                            endpoint: `organizations`,
                            id: data.id,
                            onSuccess: () => queryClient.invalidateQueries(['organizations', queryString]),
                          }),
                      },
                    ],
                  }}
                />
              ),
            ],
          },
          empty: {
            title: 'No organizations found!',
            text: 'Create an organization to begin.',
            icon: 'apartment',
          },
        }}
      />
      {createOrgModal && <CreateOrgModal onClose={() => setCreateOrgModal(false)} />}
    </Page>
  )
}

export default Organizations
