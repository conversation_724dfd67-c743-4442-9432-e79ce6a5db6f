import { Input, Modal, Select } from '@bitcine/cinesend-theme/dist'
import { useState } from 'react'
import useStore from '../../hooks/use_store'
import { useQuery } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'

const CreateOrgModal = ({ onClose }) => {
  const [name, setName] = useState('')
  const [userID, setUserID] = useState(null)
  const { createModel } = useStore()
  const { data: users } = useQuery(['users'])
  const navigate = useNavigate()

  const create = () => {
    createModel.mutate(
      {
        endpoint: 'organizations',
        data: {
          name,
          owner_id: userID,
        },
      },
      {
        onSuccess: (res) => {
          navigate(`/settings/organizations/${res.data.organization.id}`)
        },
      }
    )
  }
  return (
    <Modal
      header={`Create User`}
      onClose={onClose}
      confirmButton={{
        text: 'Create',
        disabled: name.length === 0 || !userID,
        onClick: () => create(),
      }}
    >
      <div className='space-y-2'>
        <Input value={name} label='Name' onChange={(e) => setName(e.target.value)} />
        <Select
          label='Organization Owner'
          value={userID ? { value: userID, label: users?.find((opt) => opt.id === userID)?.name } : null}
          options={users?.map((opt) => ({ value: opt.id, label: opt.name }))}
          onChange={(opt) => setUserID(opt.value)}
        />
      </div>
    </Modal>
  )
}

export default CreateOrgModal
