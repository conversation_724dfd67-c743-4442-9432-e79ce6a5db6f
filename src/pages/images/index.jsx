import Page from '../../components/layouts/page'
import { Navigate, Route, Routes } from 'react-router-dom'
import UploadImagesButton from './upload_images_button'
import Tabs from '../../components/layouts/tabs'
import ImagesTable from '../../components/images_table'

const Images = () => {
  return (
    <Page title={'Images'} buttons={<UploadImagesButton />}>
      <div className='space-y-4'>
        <Tabs
          wide
          list={[
            {
              name: 'Unmatched Only',
              to: 'unmatched',
            },
            {
              name: 'Matched Only',
              to: 'matched',
            },
            {
              name: 'All Images',
              to: 'all',
            },
          ]}
        />
        <Routes>
          <Route path='unmatched' element={<ImagesTable type='unmatched' />} />
          <Route path='matched' element={<ImagesTable type='matched' />} />
          <Route path='all' element={<ImagesTable type='all' />} />
          <Route path='*' element={<Navigate to='unmatched' />} />
        </Routes>
      </div>
    </Page>
  )
}

export default Images
