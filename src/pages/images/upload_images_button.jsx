import { Button, Icon, Modal, ProgressBar, Table, Tag } from '@bitcine/cinesend-theme'
import { useRef, useState } from 'react'
import useStore from '../../hooks/use_store'
import { humanFileSize } from '../../helpers/human_file_size'
import { useParams } from 'react-router-dom'

const UploadImagesModal = () => {
  const inputRef = useRef()

  const [files, setFiles] = useState()
  const [isUploading, setIsUploading] = useState(false)
  const [progress, setProgress] = useState(0)
  const [complete, setComplete] = useState(false)
  const [showModal, setShowModal] = useState(false)

  const { cycleID, airlineID } = useParams()
  const { createModel, updateModel } = useStore()

  const createImageModel = (index) => {
    const file = files[index]
    createModel.mutate(
      {
        endpoint: '/asset-images',
        silent: true,
        data: {
          file_name: file.name,
          size: file.size,
          mime: file.type,
          cycle_id: cycleID,
          airline_id: airlineID,
        },
      },
      {
        onSuccess: (response) => {
          if (response.data.signed_upload_url) {
            uploadToS3(response.data.signed_upload_url, file, response.data.asset_image.id)
          } else {
            // handle error?
          }
        },
        onSettled: () => {
          if (index + 1 < files.length) {
            createImageModel(index + 1)
            setProgress(index + 1)
          } else {
            setComplete(true)
          }
        },
      }
    )
  }

  const handleImages = () => {
    setIsUploading(true)
    createImageModel(0)
  }

  const uploadToS3 = (signedUrl, file, assetImageID) => {
    fetch(signedUrl, {
      body: file,
      method: 'PUT',
      mode: 'cors',
      headers: {
        'Content-Type': file.type,
      },
    })
      .then(() => {
        updateModel.mutate({
          endpoint: '/asset-images',
          id: assetImageID,
          silent: true,
          data: {
            status: 'uploaded',
          },
        })
      })
      .catch((err) => console.log(err))
  }

  return (
    <>
      <Button icon='image' onClick={() => inputRef.current.click()}>
        Upload images
      </Button>
      <input
        ref={inputRef}
        type='file'
        id='file'
        multiple
        className='hidden'
        accept='image/jpeg'
        onChange={(e) => {
          setFiles(Array.from(e.target.files))
          setShowModal(true)
        }}
      />
      {showModal && (
        <Modal
          header='Upload Images'
          className='w-4/5'
          onClose={() => {
            if (isUploading && !complete) {
              if (window.alert('Please wait for the uploads to finish before closing the modal.')) {
                return
              }
            }
            setShowModal(false)
            setComplete(false)
            setIsUploading(false)
            setFiles([])
            setProgress(0)
          }}
          confirmButton={{
            text: 'Upload',
            disabled: isUploading,
            onClick: () => handleImages(),
          }}
        >
          {complete ? (
            <div className='bg-success-100 p-8 flex items-center justify-center flex-col rounded'>
              <Icon icon='check_circle_outline' className='text-success-600 text-4xl' />
              <div className='text-center text-lg font-semibold mt-4'>Upload complete!</div>
            </div>
          ) : (
            <div>
              <div className='overflow-y-scroll max-h-[400px]'>
                <Table
                  widths={[100, 'auto', 140]}
                  header={{
                    columns: [
                      { text: 'Preview', key: 'preview' },
                      { text: 'File name', key: 'file_name' },
                      { text: 'File size', key: 'file_size' },
                    ],
                  }}
                  body={{
                    data: files,
                    row: {
                      compact: true,
                      render: [
                        (data) => (
                          <img src={URL.createObjectURL(data)} alt={data.name} className='max-h-10 object-cover' />
                        ),
                        (data) => data.name,
                        (data) => humanFileSize(data.size),
                      ],
                    },
                  }}
                />
              </div>
              <div className='mt-4 px-4 space-y-2 border-t pt-4'>
                {progress > 0 && <ProgressBar completed={(progress / files.length) * 100} displayPercentage />}
                <div className='text-xs flex justify-end'>
                  {progress} of {files.length} images uploaded...
                </div>
              </div>
            </div>
          )}
        </Modal>
      )}
    </>
  )
}

export default UploadImagesModal
