import { Icon, Modal } from '@bitcine/cinesend-theme/dist'
import { humanFileSize } from '../../helpers/human_file_size'
import { convertToLocalWithTimezone } from '../../helpers/convert_date'
import { useQuery } from '@tanstack/react-query'

const ImageModal = ({ image, onClose }) => {
  const { data: imageData, isLoading } = useQuery([`/asset-images/${image.id}`])
  return (
    <Modal header={image.file_name} pending={isLoading} onClose={onClose}>
      <div className='flex flex-col space-y-4'>
        {image.asset_id && image.field_id && (
          <div className='bg-success-200 px-4 py-4 text-xs font-semibold rounded space-y-2 flex flex-col items-center justify-center'>
            <Icon icon='check_circle_outline' className='text-success-600 text-4xl' />
            <div>Image matched successfully!</div>
            <div>Asset: {image?.asset?.title}</div>
            <div>Field: {image?.field?.name}</div>
          </div>
        )}
        <img
          src={imageData?.asset_image?.signed_url}
          alt={image?.file_name}
          className='w-full max-h-96 object-contain'
        />
        <div className='bg-neutral-100 px-4 py-2 text-xs rounded space-y-1'>
          <div className='flex justify-end'>File name: {image?.file_name}</div>
          <div className='flex justify-end'>File size: {humanFileSize(image?.size)}</div>
          <div className='flex justify-end'>Created at: {convertToLocalWithTimezone(image?.created_at)}</div>
        </div>
      </div>
    </Modal>
  )
}

export default ImageModal
