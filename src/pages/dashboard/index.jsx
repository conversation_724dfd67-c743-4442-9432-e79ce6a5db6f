import { useParams } from 'react-router-dom'
import Page from '../../components/layouts/page'
import { useQuery } from '@tanstack/react-query'
import { Icon } from '@bitcine/cinesend-theme/dist'
import Snapshots from './snapshots'

const Dashboard = () => {
  const { airlineID, cycleID } = useParams()
  const { data: airline } = useQuery(['airlines/', airlineID])
  const { data: cycle } = useQuery(['cycles/', cycleID])
  return (
    <Page title='Dashboard'>
      <div className='flex flex-col items-center justify-center h-full space-y-2 py-8 bg-primary-100 rounded'>
        <Icon icon='airplane_ticket' className='text-4xl' />
        <div className='font-semibold text-xl'>{airline?.name}</div>
        <div className='font-medium text-base'>{cycle?.description}</div>
        <div>Dashboard stats go here.</div>
      </div>
      <Snapshots cycleID={cycleID} />
    </Page>
  )
}

export default Dashboard
