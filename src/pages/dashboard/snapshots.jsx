import { Button, ButtonDropdown, Message, Status, Table, Tag } from '@bitcine/cinesend-theme/dist'
import { useSnackbar } from '../../hooks/use_snackbar'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { convertToLocalDateTime } from '../../helpers/convert_date'
import useStore from '../../hooks/use_store'
import axios from 'axios'

export default function Snapshots({ cycleID }) {
  const { data, isLoading, error } = useQuery([`snapshots?cycle_id=${cycleID}`])
  const snapshots = data?.data || []
  const { addMessage } = useSnackbar()
  const queryClient = useQueryClient()
  const { createModel, deleteModel } = useStore()
  const generateSnapshot = () =>
    createModel.mutate(
      {
        endpoint: 'snapshots',
        data: {
          cycle_id: cycleID,
        },
      },
      {
        onSuccess: () => {
          addMessage('Snapshot generation triggered successfully.')
          queryClient.invalidateQueries(['snapshots'])
        },
        onError: (error) => {
          addMessage(`Error triggering snapshot generation: ${error.response?.data?.message || error.message}`, 'error')
        },
      }
    )
  const deleteSnapshot = (snapshot) => {
    deleteModel.mutate({
      endpoint: 'snapshots',
      id: snapshot.id,
    })
  }
  const sendSnapshotToViasat = useMutation(
    (snapshotID) => {
      return axios.post(`snapshots/${snapshotID}/send-to-viasat`)
    },
    {
      onSuccess: () => {
        addMessage('Snapshot sent to Viasat API successfully.')
        queryClient.invalidateQueries(['snapshots'])
      },
      onError: (error) => {
        addMessage(`Error sending snapshot to Viasat API: ${error.response?.data?.message || error.message}`, 'error')
      },
    }
  )
  return (
    <div className='border-t mt-4 pt-4'>
      <div className='flex items-center justify-between border-b pb-4'>
        <div className='text-xl font-semibold'>Cycle Snapshots</div>
        <Button key={'generate-snapshot-button'} size='small' type='success' onClick={() => generateSnapshot()}>
          Generate new snapshot
        </Button>
      </div>
      <Message info small>
        Snapshots contain category and asset metadata for a specific point in time for a cycle. A new snapshot should be
        generated if metadata is updated. Images in snapshots are signed for 15 minutes.
      </Message>
      <Status pending={isLoading} error={error}>
        <Table
          widths={[200, 150, 100, 200, 55]}
          header={{
            columns: [
              { text: 'Filename', key: 'filename' },
              { text: 'Created At', key: 'created_at' },
              { text: 'Status', key: 'status' },
              { text: '' },
              { text: '' },
            ],
          }}
          body={{
            data: snapshots,
            row: {
              compact: true,
              spaced: true,
              render: [
                (data) => data.filename,
                (data) => convertToLocalDateTime(data.created_at),
                (data) => (
                  <Tag
                    outline
                    type={data.is_completed ? 'success' : data.is_failed ? 'error' : 'warning'}
                    label={
                      data.is_completed
                        ? 'Complete'
                        : data.is_failed
                          ? 'Failed'
                          : `In Progress: ${parseInt(data.progress)}%`
                    }
                  />
                ),
                (data) => (
                  <div className='flex space-x-2'>
                    <Button size='small' onClick={() => sendSnapshotToViasat.mutate(data.id)}>
                      Send to Viasat API
                    </Button>
                    <Button size='small' onClick={() => window.open(`${data.signed_url}`, '_blank')}>
                      View snapshot
                    </Button>
                  </div>
                ),
                (data) => (
                  <ButtonDropdown
                    kebab
                    dropdown={{
                      content: [
                        {
                          text: 'Delete',
                          className: 'text-red-500',
                          onClick: () => deleteSnapshot(data),
                        },
                      ],
                    }}
                  />
                ),
              ],
            },
          }}
          empty={{
            message: 'No snapshots available for this cycle.',
          }}
        />
      </Status>
    </div>
  )
}
