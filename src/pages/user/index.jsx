import { useQuery, useQueryClient } from '@tanstack/react-query'
import Page from '../../components/layouts/page'
import { useParams } from 'react-router-dom'
import { Select } from '@bitcine/cinesend-theme/dist'
import useStore from '../../hooks/use_store'

const User = () => {
  const { userID } = useParams()
  const queryKey = ['users/', userID]
  const { data: user, isLoading } = useQuery(queryKey)
  const { data: roles } = useQuery(['roles'])
  const { data: organizations } = useQuery(['organizations'])
  const { updateModel } = useStore()
  const queryClient = useQueryClient()

  const update = (value, isLocal) => {
    if (isLocal) {
      queryClient.setQueryData(queryKey, () => ({
        ...user,
        ...value,
      }))
    } else {
      updateModel.mutate({
        endpoint: 'users',
        id: userID,
        data: value,
      })
    }
  }

  return (
    <Page breadcrumbs={[{ text: 'Users', to: '/settings/users' }, { text: user?.name }]}>
      <div className='flex flex-col space-y-4'>
        <Select
          label='Role'
          value={roles?.find((opt) => opt.id === user?.role_id)}
          isLoading={isLoading}
          options={roles?.map((opt) => ({ value: opt.id, label: opt.label }))}
          onChange={(opt) => update({ role_id: opt.value }, false)}
        />
        <Select
          label='Organization'
          value={
            user?.organization_id
              ? {
                  value: user.organization_id,
                  label: organizations?.find((opt) => opt.id === user.organization_id)?.name,
                }
              : null
          }
          isLoading={isLoading}
          options={organizations?.map((opt) => ({ value: opt.id, label: opt.name }))}
          onChange={(opt) => update({ organization_id: opt.value }, false)}
        />
      </div>
    </Page>
  )
}

export default User
