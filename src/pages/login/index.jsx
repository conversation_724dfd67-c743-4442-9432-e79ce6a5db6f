import { Button, Input } from '@bitcine/cinesend-theme'
import { useState } from 'react'
import axios from 'axios'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'

const Login = ({ organization }) => {
  const navigate = useNavigate()
  const [email, setEmail] = useState(process.env.VITE_EMAIL || '')
  const [password, setPassword] = useState(process.env.VITE_PASSWORD || '')
  const queryClient = useQueryClient()
  const logIn = useMutation(
    ({ email, password }) =>
      axios.post(`/token-sign-in`, {
        email,
        password,
      }),
    {
      onSuccess: (res) => {
        // set as default for all reqs.
        localStorage.setItem('user_token', res.data.token)
        axios.defaults.headers.common = {
          Authorization: `Bearer ${res.data.token}`,
        }
        queryClient.invalidateQueries(['/user'])
        navigate('/')
      },
    }
  )
  return (
    <>
      <div className='flex items-center justify-center h-screen w-full bg-neutral-800'>
        <div className='w-full max-w-sm flex flex-col space-y-4 bg-neutral-200 rounded-lg shadow-lg p-8'>
          <div className='max-w-xs w-full px-8 mb-4 justify-center flex'>
            <img src={organization?.login_logo_url} />
          </div>
          <form className='space-y-2'>
            <Input
              label='Email'
              placeholder='<EMAIL>'
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
            <Input label='Password' type='password' value={password} onChange={(e) => setPassword(e.target.value)} />
            <div className='pt-4'>
              <Button
                className='w-full'
                disabled={!email || !password || logIn.isLoading}
                onClick={() => {
                  logIn.mutate({ email, password })
                }}
              >
                Login
              </Button>
            </div>
            {logIn.error?.message && <div className='text-error-600 text-xs'>{logIn.error?.message}</div>}
          </form>
          <Button link onClick={() => navigate('/forgot-password')}>
            Forgot Password?
          </Button>
        </div>
      </div>
    </>
  )
}

export default Login
