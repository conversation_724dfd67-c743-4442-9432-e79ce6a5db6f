import { useQuery } from '@tanstack/react-query'
import { Navigate, Route, Routes, useParams } from 'react-router-dom'
import Fields from './fields'
import Field from '../../components/field'
import Page from '../../components/layouts/page'
import Lists from './lists'
import List from '../../components/list'

const Schema = ({ airline }) => {
  const schemaID = useParams().schemaID
  const fieldID = useParams().fieldID
  const { data: schema } = useQuery([`schemas/`, `${schemaID}`])
  return (
    <Page
      breadcrumbs={[{ text: 'Schemas', to: '/settings/schemas' }, { text: schema?.name }]}
      tabs={[
        { name: 'Fields', to: `fields` },
        { name: 'Lists', to: `lists` },
      ]}
    >
      {schema ? (
        <Routes>
          <Route path='/fields' element={<Fields schemaID={schemaID} airline={airline} />} />
          <Route path='/fields/:fieldID' element={<Field airline={airline} schema={schema} fieldID={fieldID} />} />
          <Route path='/lists' element={<Lists schema={schema} />} />
          <Route path='/lists/:listID' element={<List schema={schema} />} />
          <Route path='*' element={<Navigate to={`fields`} replace={true} />} />
        </Routes>
      ) : null}
    </Page>
  )
}

export default Schema
