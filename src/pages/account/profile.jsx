import { Button, Input } from '@bitcine/cinesend-theme'
import { useState } from 'react'

export default function Profile({ user, updateUser }) {
  const [name, setName] = useState(user.name)
  return (
    <div className='space-y-4'>
      <Input label='Name' value={name} onChange={(e) => setName(e.target.value)} />
      <div className='flex justify-end'>
        <Button disabled={name === user.name} onClick={() => updateUser({ name })}>
          Save
        </Button>
      </div>
    </div>
  )
}
