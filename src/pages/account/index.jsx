import { Navigate, Route, Routes } from 'react-router-dom'
import Page from '../../components/layouts/page'
import Profile from './profile'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import Security from './security'
import useStore from '../../hooks/use_store'

export default function Account() {
  const { data: user, isLoading } = useQuery(['/user'])
  const { updateModel } = useStore()
  const queryClient = useQueryClient()
  const updateUser = (data) => {
    updateModel.mutate(
      { endpoint: 'users', id: user.id, data },
      {
        onSuccess: () => {
          queryClient.invalidateQueries(['/user'])
        },
      }
    )
  }
  return (
    <Page
      title='My Account'
      pending={isLoading}
      tabs={[
        {
          name: 'My Profile',
          to: 'profile',
        },
        {
          name: 'Security',
          to: 'security',
        },
      ]}
    >
      <Routes>
        <Route exact path='' element={<Navigate to='profile' replace />} />
        <Route path='profile' element={<Profile user={user} updateUser={updateUser} />} />
        <Route path='security' element={<Security user={user} updateUser={updateUser} />} />
      </Routes>
    </Page>
  )
}
