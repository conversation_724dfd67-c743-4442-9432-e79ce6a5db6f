import { Button, Input } from '@bitcine/cinesend-theme'
import { useState } from 'react'

export default function Security({ updateUser }) {
  const [newPassword, setNewPassword] = useState('')
  const [confirmPassword, setConfirmPassword] = useState('')
  return (
    <div className='space-y-4'>
      <Input
        label='New Password'
        type='password'
        value={newPassword}
        onChange={(e) => setNewPassword(e.target.value)}
      />
      <Input
        label='Confirm Password'
        type='password'
        value={confirmPassword}
        onChange={(e) => setConfirmPassword(e.target.value)}
      />
      <div className='text-xs bg-warning-100 rounded border p-2'>
        Your password must be at least 8 characters and contain at least one uppercase character, one number, and one
        special character.
      </div>
      <div className='flex justify-end'>
        <Button
          disabled={newPassword !== confirmPassword || !newPassword}
          onClick={() =>
            updateUser({
              password: newPassword,
              password_confirmation: confirmPassword,
            })
          }
        >
          Save
        </Button>
      </div>
    </div>
  )
}
