import Page from '../../components/layouts/page'
import UsersTable from '../../components/users_table'
import { Button } from '@bitcine/cinesend-theme/dist'
import { useState } from 'react'
import CreateUserModal from './create_user_modal'

const Users = () => {
  const [showUserModal, setShowUserModal] = useState(false)

  return (
    <Page
      title='Users'
      buttons={[
        <Button key={'create_user_button'} onClick={() => setShowUserModal(true)}>
          Create User
        </Button>,
      ]}
    >
      <UsersTable queryKey={['users']} />
      {showUserModal && <CreateUserModal onClose={() => setShowUserModal(false)} />}
    </Page>
  )
}

export default Users
