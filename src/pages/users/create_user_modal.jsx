import { Input, Modal, Select } from '@bitcine/cinesend-theme/dist'
import { useState } from 'react'
import useStore from '../../hooks/use_store'
import { useQuery } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'

const CreateUserModal = ({ onClose }) => {
  const [name, setName] = useState('')
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [roleID, setRoleID] = useState('')
  const [orgID, setOrgID] = useState('')
  const { createModel } = useStore()
  const { data: roles } = useQuery(['roles'])
  const { data: organizations } = useQuery(['organizations'])
  const navigate = useNavigate()

  const create = () => {
    createModel.mutate(
      {
        endpoint: 'users',
        data: {
          name,
          email,
          password,
          role_id: roleID,
          organization_id: orgID,
        },
      },
      {
        onSuccess: (res) => {
          navigate(`/settings/users/${res.data.user.id}`)
        },
      }
    )
  }
  return (
    <Modal
      header={`Create User`}
      onClose={onClose}
      confirmButton={{
        text: 'Create',
        disabled:
          name.length === 0 || email.length === 0 || roleID.length === 0 || orgID.length === 0 || password.length <= 7,
        onClick: () => create(),
      }}
    >
      <div className='space-y-2'>
        <Input value={name} label='Name' onChange={(e) => setName(e.target.value)} />
        <Input value={email} label='Email' onChange={(e) => setEmail(e.target.value)} />
        <Input value={password} label='Password' onChange={(e) => setPassword(e.target.value)} type='password' />
        <Select
          label='Role'
          value={roleID ? { value: roleID, label: roles?.find((opt) => opt.id === roleID)?.label } : null}
          options={roles?.map((opt) => ({ value: opt.id, label: opt.label }))}
          onChange={(opt) => setRoleID(opt.value)}
        />
        <Select
          label='Organization'
          value={orgID ? { value: orgID, label: organizations?.find((opt) => opt.id === orgID)?.name } : null}
          options={organizations?.map((opt) => ({ value: opt.id, label: opt.name }))}
          onChange={(opt) => setOrgID(opt.value)}
        />
      </div>
    </Modal>
  )
}

export default CreateUserModal
