import { Input, Message } from '@bitcine/cinesend-theme/dist'
import { useState } from 'react'
import useStore from '../../../hooks/use_store'

const InputLocalized = ({ categoryID, langID, label, defaultValue, disabled = false }) => {
  const [value, setValue] = useState(defaultValue)
  const [error, setError] = useState(null)
  const { createModel } = useStore()

  const updateTitle = (val) => {
    createModel.mutate(
      {
        endpoint: 'category-values',
        data: {
          category_id: categoryID,
          value: val,
          language_id: langID,
        },
      },
      {
        onSuccess: () => setError(null),
        onError: (err) => setError(err.response.data.message),
      }
    )
  }

  return (
    <div className='w-full'>
      <Input
        label={label}
        value={value}
        error={error}
        onChange={(e) => setValue(e.target.value)}
        onBlur={(e) => updateTitle(e.target.value)}
        disabled={disabled}
      />
      {error && (
        <Message error={error} small={true}>
          {error}
        </Message>
      )}
    </div>
  )
}

export default InputLocalized
