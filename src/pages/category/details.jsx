import { DatePicker, Message, Select, Status } from '@bitcine/cinesend-theme/dist'
import useUtilities from '../../hooks/use_utilities'
import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import InputLocalized from './components/input_localized'
import useAuth from '../../hooks/use_auth'
import { modelTypes } from '../../constants/model_types'

const Details = ({ category, update }) => {
  const [currentCategory, setCurrentCategory] = useState(category)
  const { assetTypes } = useUtilities()
  const pluralAssetTypes = assetTypes?.map((opt) => ({ ...opt, label: opt.plural_label }))
  const { data: airline, isLoading, isError, error } = useQuery([`airlines/${category.airline_id}`])
  const { canEditModel: canEditCategory } = useAuth(modelTypes.Category, category)

  const disabled = !canEditCategory

  return (
    <div className='space-y-2'>
      <Status pending={isLoading} isError={isError} errorMessage={error?.message}>
        {category.issues.length > 0 && (
          <Message error={true} small={true} className='mb-2'>
            {category.issues[0]?.description}
          </Message>
        )}
        <div className='flex items-center space-x-4'>
          {airline?.languages?.map((lang) => {
            return (
              <InputLocalized
                categoryID={category.id}
                langID={lang.id}
                key={`title-${lang.id}`}
                label={`${lang?.eng_description} Title`}
                defaultValue={category.category_values?.find((v) => v.language_id === lang.id)?.value}
                disabled={disabled}
              />
            )
          })}
        </div>
      </Status>

      <Select
        label='Asset Types'
        placeholder='Movies, series, audiobooks, etc.'
        options={pluralAssetTypes?.filter((opt) => airline?.asset_types?.includes(opt.value))}
        isMulti
        value={pluralAssetTypes?.filter((opt) => currentCategory?.asset_types?.includes(opt.value))}
        onChange={(options) => {
          update({ asset_types: options.map((v) => v.value) })
          setCurrentCategory({ ...currentCategory, asset_types: options.map((v) => v.value) })
        }}
        disabled={disabled}
      />
      <DatePicker
        label={'Start Date'}
        className='w-full'
        date={category.start_date}
        onChange={(newDate) => update({ start_date: newDate }, false)}
        error={false}
        disabled={disabled}
      />
      <DatePicker
        label={'End Date'}
        className='w-full'
        date={category.end_date}
        onChange={(newDate) => update({ end_date: newDate }, false)}
        error={false}
        disabled={disabled}
      />
    </div>
  )
}

export default Details
