import Page from '../../components/layouts/page'
import CyclesTable from '../../components/cycles_table'
import { useQuery } from '@tanstack/react-query'

const Cycles = () => {
  // Pass in the empty search to avoid a refetch if a user navigates to the assets table
  const { data: airlines } = useQuery(['airlines', `?search=`])
  return (
    <Page
      title={'Cycles'}
      buttons={
        [
          // <Button onClick={() => createModel.mutate({ endpoint: 'cycles', data: { name: 'Test' } })}>Create cycle</Button>
        ]
      }
    >
      <CyclesTable queryKey={'/cycles'} airlines={airlines} />
    </Page>
  )
}

export default Cycles
