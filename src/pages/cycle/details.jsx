import { DatePicker, SegmentedControl, Select, Input } from '@bitcine/cinesend-theme'
import { useEffect, useState } from 'react'
import useStore from '../../hooks/use_store'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { humanFileSize } from '../../helpers/human_file_size'
import useAuth from '../../hooks/use_auth'
import { modelTypes } from '../../constants/model_types'

const Details = ({ cycle }) => {
  const { updateModel } = useStore()
  const [state, setState] = useState(cycle)
  const updateState = (data) => setState({ ...state, ...data })
  const queryClient = useQueryClient()
  const { canEditModel: canEditCycle } = useAuth(modelTypes.Cycle, cycle)

  const saveModel = (data) => {
    updateModel.mutate(
      {
        endpoint: 'cycles',
        id: cycle.id,
        data,
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries(['cycles/'])
        },
      }
    )
  }
  const updateField = (data) => {
    updateState(data)
    saveModel(data)
  }
  const { data, isLoading } = useQuery(['schemas'])
  const schemaOptions = data ? data.map((schema) => ({ value: schema.id, label: schema.name })) : []

  useEffect(() => {
    if (cycle.total_snapshot_size !== state.total_snapshot_size) {
      setState(cycle)
    }
  }, [cycle])

  const disabled = !canEditCycle
  return (
    <div className='flex flex-col space-y-4'>
      <DatePicker
        label='Cycle Start Date'
        date={state.start_date}
        onChange={(date) => updateField({ start_date: date })}
        disabled={disabled}
      />
      <DatePicker
        label='Cycle End Date'
        date={state.end_date}
        onChange={(date) => updateField({ end_date: date })}
        disabled={disabled}
      />
      <Select
        label='Schema'
        value={schemaOptions.find((opt) => opt.value === state.schema_id)}
        isLoading={isLoading}
        options={schemaOptions}
        onChange={(opt) => updateField({ schema_id: opt.value })}
        disabled={disabled}
      />
      <div className='pt-2 flex-col md:flex md:flex-row space-x-0 md:space-x-4 space-y-2 md:space-y-0'>
        <Input label='Total Snapshot Size' disabled={true} value={humanFileSize(state.total_snapshot_size)} />
        <Input label='Cycle Recalled Assets Size' disabled={true} value={humanFileSize(state.recalled_snapshot_size)} />
      </div>
      <SegmentedControl
        options={[
          { label: 'Open', value: 'open' },
          { label: 'Closed', value: 'closed' },
        ]}
        value={state.is_locked ? 'closed' : 'open'}
        onChange={(value) => updateField({ is_locked: value === 'closed' })}
        disabled={disabled}
      />
      {/* <Button onClick={() => navigate(`/schemas/${cycle.schema_id}`)}>
        View Schema Definition
      </Button>
      <Button onClick={() => createModel.mutate({
          endpoint: 'schemas',
          data: {
            schemaID: cycle.schema_id
          }
        })}>
        Create New Schema
      </Button> */}
    </div>
  )
}

export default Details
