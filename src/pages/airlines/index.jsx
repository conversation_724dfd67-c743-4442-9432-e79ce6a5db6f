import { useState } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Button, ButtonDropdown, Table, Tag } from '@bitcine/cinesend-theme'
import Page from '../../components/layouts/page'
import useStore from '../../hooks/use_store'
import { useNavigate } from 'react-router-dom'

const Airlines = () => {
  const { createModel, deleteModel } = useStore()
  const [search, setSearch] = useState('')
  const { data: cycleFrequencies } = useQuery({ queryKey: ['/api/cycle-frequencies'] })
  const { data: airlines, error, isLoading } = useQuery(['airlines', `?search=${search}`])
  const navigate = useNavigate()
  return (
    <Page
      title={'Airlines'}
      buttons={[
        <Button
          key={'Airlines'}
          onClick={() =>
            createModel.mutate(
              { endpoint: 'airlines', data: { name: 'New Airline', cycle_frequency: 'monthly' } },
              {
                onSuccess: (res) => {
                  navigate(`${res.data.airline.id}`)
                },
              }
            )
          }
        >
          Create airline
        </Button>,
      ]}
    >
      <Table
        status={{
          pending: isLoading,
          error: error,
          errorMessage: error?.message,
        }}
        widths={[100, 100, 200, 100, 65]}
        header={{
          columns: [
            { text: 'Name', key: 'name' },
            { text: 'ICAO code', key: 'icao_code' },
            { text: 'Portal Languages', key: '' },
            { text: 'Cycle Frequency', key: 'cycle_frequency' },
            { text: '' },
          ],
          searching: {
            searchPlaceholder: 'Enter a search term here...',
            search,
            onSearch: (term) => setSearch(term),
          },
        }}
        body={{
          data: airlines,
          row: {
            compact: true,
            spaced: true,
            onClick: (event, data) => navigate(`${data.id}`),
            render: [
              (data) => data.name,
              (data) => data.icao_code,
              (data) =>
                data.languages ? (
                  <div className='flex space-x-2'>
                    {data.languages.map((opt) => (
                      <Tag key={opt.id} outline label={opt.eng_description.toUpperCase()} />
                    ))}
                  </div>
                ) : null,
              (data) => (
                <Tag
                  outline
                  type='warning'
                  label={(
                    cycleFrequencies?.find((v) => v.value === data.cycle_frequency)?.label ?? data.cycle_frequency
                  ).toUpperCase()}
                />
              ),
              (data) => (
                <ButtonDropdown
                  kebab
                  dropdown={{
                    content: [
                      {
                        text: 'Delete',
                        className: 'text-red-500',
                        icon: 'delete_forever',
                        onClick: () => deleteModel.mutate({ endpoint: 'airlines', id: data.id }),
                      },
                    ],
                  }}
                />
              ),
            ],
          },
          empty: {
            title: 'No airlines found!',
            text: 'Create a new airline to begin.',
            icon: 'flight',
          },
        }}
      />
    </Page>
  )
}

export default Airlines
