import { Navigate, Route, Routes, useParams, useNavigate } from 'react-router-dom'
import Page from '../../components/layouts/page'
import Cycles from './cycles'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@bitcine/cinesend-theme'
import useStore from '../../hooks/use_store'
import Details from './details'
import Schemas from './schemas'

const Airline = () => {
  const params = useParams()
  const navigate = useNavigate()
  const { data: airline, isLoading } = useQuery(['airlines/', params.airlineID])
  const { createModel } = useStore()
  return (
    <Page
      pending={isLoading}
      breadcrumbs={[{ text: 'Airlines', to: '/settings/airlines' }, { text: airline?.name }]}
      tabs={[
        { name: 'Cycles', to: `cycles` },
        { name: 'Schemas', to: `schemas` },
        { name: 'Airline Details', to: `details` },
      ]}
      title={airline?.name}
      buttons={[
        <div key='airline_buttons' className='flex gap-2'>
          <Button
            onClick={() =>
              createModel.mutate(
                { endpoint: 'cycles', data: { airline_id: params.airlineID } },
                { onSuccess: (res) => navigate(`/settings/airlines/${params.airlineID}/cycles/${res.data.cycle.id}`) }
              )
            }
          >
            Create cycle
          </Button>
        </div>,
      ]}
    >
      <Routes>
        <Route path='/cycles' element={<Cycles airlineID={params.airlineID} />} />
        <Route path='/schemas' element={<Schemas airline={airline} />} />
        <Route path='/details' element={<Details airline={airline} />} />
        <Route path='*' element={<Navigate to={`cycles`} replace={true} />} />
      </Routes>
    </Page>
  )
}

export default Airline
