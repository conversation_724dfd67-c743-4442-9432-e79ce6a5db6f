import { Select, Input, FileUpload } from '@bitcine/cinesend-theme'
import { useState } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import useStore from '../../hooks/use_store'
import { useSnackbar } from '../../hooks/use_snackbar'
import useUtilities from '../../hooks/use_utilities'
import AirlineSnapshotSizeInput from '../../components/airline_snapshot_size_input'

const Details = ({ airline }) => {
  const { addErrorMessage } = useSnackbar()
  const { data: languages } = useQuery(['/api/languages'])
  const { data: cycleFrequencies } = useQuery(['/api/cycle-frequencies'])
  const { assetTypes } = useUtilities()
  const { updateModel } = useStore()
  const [state, setState] = useState(airline)
  const updateState = (data) => setState({ ...state, ...data })
  const queryClient = useQueryClient()
  const saveModel = (data) => {
    updateState(data)
    updateModel.mutate(
      {
        endpoint: 'airlines',
        id: airline.id,
        data,
      },
      {
        onSuccess: () => queryClient.setQueryData(['airlines/', airline.id], { ...airline, ...data }),
      }
    )
  }
  const pluralAssetTypes = assetTypes?.map((opt) => ({ ...opt, label: opt.plural_label }))
  return (
    <div className='grid grid-cols-2 gap-3'>
      <div className='space-y-4 mt-4'>
        <Input
          label='Name'
          value={state?.name}
          onChange={(e) => updateState({ name: e.target.value })}
          onBlur={() => saveModel({ name: state.name })}
        />

        <Input
          label='ICAO code'
          value={state?.icao_code}
          onChange={(e) => updateState({ icao_code: e.target.value })}
          onBlur={() => saveModel({ icao_code: state.icao_code })}
        />

        <Select
          label='Asset Types'
          placeholder='Movies, series, audiobooks, etc.'
          options={pluralAssetTypes}
          isMulti
          value={pluralAssetTypes?.filter((opt) => state?.asset_types?.includes(opt.value))}
          onChange={(options) => {
            saveModel({ asset_types: options.map((v) => v.value) })
          }}
        />

        <Select
          label='Cycle Frequency'
          placeholder='(monthly, every 2-months, quarterly, etc.)'
          className={'w-full'}
          options={cycleFrequencies}
          value={cycleFrequencies?.find((opt) => opt.value === state.cycle_frequency)}
          onChange={(value) => {
            saveModel({ cycle_frequency: value.value })
          }}
        />

        <Select
          label='Portal Languages'
          placeholder={'Portal languages'}
          className={'w-full'}
          options={languages ? languages.map((option) => ({ value: option.id, label: option.eng_description })) : []}
          value={languages
            ?.filter((obj1) => state.languages?.some((obj2) => obj1.id === obj2.id || obj1.id === obj2))
            .map((option) => ({ value: option.id, label: option.eng_description }))}
          isMulti={true}
          onChange={(list) => {
            const langs = list.map((v) => v.value)
            saveModel({ languages: langs })
          }}
        />
        <Input
          label='Recall Period (Days)'
          value={state.recall_period}
          type='number'
          onChange={(e) => updateState({ recall_period: e.target.value })}
          onBlur={() => saveModel({ recall_period: state.recall_period })}
        />
      </div>
      <div className='space-y-4 mt-4'>
        <FileUpload
          className={'h-40'}
          backgroundImage={{
            url: state.logo_url,
          }}
          includeRemoveButton={false}
          upload={{
            message: 'Drop image here',
            accept: {
              'image/*': ['.jpg', '.jpeg', '.png'],
            },
            icon: 'image',
            publicURL: `${import.meta.env.VITE_API_URL}/api/airlines/upload-logo/${airline.id}`,
            onComplete: () => {
              // this is a bit of a hack, but we need to refetch the airline data after uploading the logo
              queryClient.fetchQuery(['airlines/', airline.id]).then((res) => updateState(res))
            },
            onError: (err) => {
              addErrorMessage(err.message)
            },
          }}
          button={{
            text: 'Upload Logo',
          }}
        />

        <AirlineSnapshotSizeInput
          fieldName='max_total_snapshot_size'
          label='Max Snapshot Size - Total'
          value={state?.max_total_snapshot_size}
          saveModel={saveModel}
        />

        <AirlineSnapshotSizeInput
          fieldName='max_delta_snapshot_size'
          label='Max Snapshot Size - Delta'
          value={state?.max_delta_snapshot_size}
          saveModel={saveModel}
        />

        <AirlineSnapshotSizeInput
          fieldName='max_for_eis_size'
          label='Max "For EIS" Size'
          value={state?.max_for_eis_size}
          saveModel={saveModel}
        />
      </div>
    </div>
  )
}

export default Details
