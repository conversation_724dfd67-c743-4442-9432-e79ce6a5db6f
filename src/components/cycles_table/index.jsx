import { useNavigate } from 'react-router-dom'
import { convertToLocal } from '../../helpers/convert_date'
import { ButtonDropdown, Table } from '@bitcine/cinesend-theme'
import useStore from '../../hooks/use_store'
import { statusColors } from '../../constants/status_colour_map'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import useTableFilters from '../../hooks/use_table_filters'

const CyclesTable = ({ queryKey, airlines = null }) => {
  const queryClient = useQueryClient()
  const filteringOptions = airlines
    ? [
        {
          label: 'Airline',
          key: 'airline_id',
          type: 'checkboxes',
          options: airlines.map((a) => ({ label: a.name, key: a.id })),
        },
      ]
    : null

  const { queryString, searching, sorting, filtering } = useTableFilters({
    filteringOptions,
    enableSearch: airlines !== null,
  })
  const { data: cycles, isLoading, error } = useQuery([queryKey, queryString])

  const navigate = useNavigate()
  const { deleteModel } = useStore()
  return (
    <Table
      status={{
        pending: isLoading,
        error: error,
        errorMessage: error?.message,
      }}
      widths={[160, 160, 140, 140, 100, 80, 80, 55]}
      header={{
        columns: [
          { text: 'Cycle', key: 'cycle' },
          { text: 'Airline', key: 'airline_id' },
          { text: 'Start', key: 'start_date' },
          { text: 'End', key: 'end_date' },
          { text: 'Counts', key: 'status_counts' },
          { text: 'Status', key: '' },
          { text: 'Schema', key: '' },
          { text: '' },
        ],
        searching,
        sorting,
        filtering,
      }}
      body={{
        data: cycles,
        row: {
          compact: true,
          spaced: true,
          onClick: (event, data) => navigate(`/airlines/${data.airline_id}/cycles/${data.id}`),
          render: [
            (data) => data.description,
            (data) => data.airline_name,
            (data) => convertToLocal(data.start_date),
            (data) => convertToLocal(data.end_date),
            ({ status_counts }) =>
              Object.values(status_counts ?? {}).reduce((sum, count) => sum + count, 0) ? (
                <div className='flex flex-row font-medium space-x-2'>
                  <div className={`${statusColors.new} pr-2 border-r-2 border-gray-200`} title='New'>
                    {status_counts.new ?? 0}
                  </div>
                  <div className={`${statusColors.holdover} pr-2 border-r-2 border-gray-200`} title='Holdover'>
                    {status_counts.holdover ?? 0}
                  </div>
                  <div className={`${statusColors.expiring} pr-2 border-r-2 border-gray-200`} title='Expiring'>
                    {status_counts.expiring ?? 0}
                  </div>
                  <div className={`${statusColors.recalled} pr-2`} title='Recalled'>
                    {status_counts.recalled ?? 0}
                  </div>
                </div>
              ) : (
                'None'
              ),

            (data) => (data.is_locked ? 'Locked' : 'Open'),
            (data) => data.schema_id,
            (data) => (
              <ButtonDropdown
                kebab
                dropdown={{
                  content: [
                    {
                      text: 'Delete',
                      className: 'text-red-500',
                      onClick: () => {
                        deleteModel.mutate(
                          { endpoint: 'cycles', id: data.id },
                          {
                            onSuccess: () => {
                              queryClient.invalidateQueries([queryKey, queryString])
                            },
                          }
                        )
                      },
                    },
                  ],
                }}
              />
            ),
          ],
        },
        empty: {
          title: 'No cycles found!',
          text: 'Create a new cycle to begin.',
          icon: 'date_range',
        },
      }}
    />
  )
}

export default CyclesTable
