import { Input } from '@bitcine/cinesend-theme/dist'
import { useState } from 'react'
import useStore from '../../hooks/use_store'

const CollectionValue = ({ collection, collectionValue, identifierCollectionValue = null, language, disabled }) => {
  const { updateModel, createModel } = useStore()
  const [label, setLabel] = useState(collectionValue ? collectionValue.label : '')

  const saveField = (newLabel) => {
    // If the collection value already exists, we're just updating the value. Otherwise, we create it.
    if (collectionValue) {
      updateModel.mutate({
        endpoint: 'collection-values',
        id: collectionValue.id,
        data: {
          label: newLabel,
        },
      })
    } else {
      createModel.mutate({
        endpoint: 'collection-values',
        data: {
          collection_id: collection.id,
          language_id: language.id,
          label: newLabel,
          collection_value_id: identifierCollectionValue ? identifierCollectionValue.id : null,
        },
      })
    }
  }

  return (
    <Input
      // label={'Value'}
      value={label}
      disabled={disabled}
      onChange={(e) => setLabel(e.target.value)}
      onBlur={(e) => {
        // Only save if the value has changed.
        if (e.target.value !== collectionValue?.label) {
          saveField(e.target.value)
        }
      }}
    />
  )
}

export default CollectionValue
