import React, { useState } from 'react'
import { ProgressBar } from '@bitcine/cinesend-theme'
import { StoreContext } from '../../contexts/store'
import { humanFileSize } from '../../helpers/human_file_size'

export default function TransfersList() {
  const [showTransfers, setShowTransfers] = useState(true)
  const { state } = React.useContext(StoreContext)

  const transfersCount = state.transfers.length

  if (!transfersCount) {
    return null
  }

  return (
    <div className='absolute bottom-4 right-4'>
      {showTransfers ? <TransfersModal transfers={state.transfers} /> : null}
      <div className='flex justify-end'>
        <div
          onClick={() => setShowTransfers(!showTransfers)}
          className='border rounded-full px-4 py-1 text-xs flex items-center justify-center bg-primary-500 text-white cursor-pointer'
        >
          {showTransfers ? 'Hide' : 'Show'} {transfersCount} upload{transfersCount === 1 ? '' : 's'}
        </div>
      </div>
    </div>
  )
}

function TransfersModal({ transfers }) {
  return (
    <div className='py-2 px-4 rounded-lg bg-white border border-gray-300 mb-2 text-xs divide-y min-w-[600px]'>
      {transfers.map((transfer, index) => (
        <Transfer key={index} transfer={transfer} />
      ))}
    </div>
  )
}

function Transfer({ transfer }) {
  return (
    <div key={transfer.id} className='flex flex-col items-center py-2 space-y-1'>
      <div className='flex items-center justify-between w-full space-x-10'>
        <div>{transfer.name}</div>
        <div>
          {humanFileSize(transfer.current_bytes)} / {humanFileSize(transfer.total_bytes)}
        </div>
      </div>
      <ProgressBar completed={(transfer.current_bytes / transfer.total_bytes) * 100} />
    </div>
  )
}
