import { useNavigate } from 'react-router-dom'
import { Icon } from '@bitcine/cinesend-theme'

const Breadcrumbs = ({ className = '', breadcrumbs = [] }) => {
  const navigate = useNavigate()
  if (breadcrumbs.length === 0) {
    return null
  }
  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {breadcrumbs.map((breadcrumb, index) => (
        <div className='flex items-center space-x-2' key={index}>
          <span
            className={`${breadcrumb.to ? 'cursor-pointer text-gray-500' : 'font-semibold'}`}
            onClick={() => navigate(breadcrumb.to)}
          >
            {breadcrumb.text}
          </span>
          {index !== breadcrumbs.length - 1 ? <Icon icon='chevron_right' className='text-sm' /> : null}
        </div>
      ))}
    </div>
  )
}

export default Breadcrumbs
