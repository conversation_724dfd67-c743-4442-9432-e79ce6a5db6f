import { Icon, Status, Message } from '@bitcine/cinesend-theme'
import Breadcrumbs from './breadcrumbs'
import Tabs from './tabs'
import { ErrorBoundary } from 'react-error-boundary'

function ErrorFallback({ error }) {
  return (
    <Message error={true} small={false}>
      <div>
        <div className='text-xl font-bold'>{`Oops. Something went wrong.`}</div>
        {error.message && <div className='mt-2 text-xs'>{error.message}</div>}
        {error.stack && <pre className='mt-2 text-xs'>{error.stack}</pre>}
      </div>
    </Message>
  )
}

const Page = ({ title, buttons = [], breadcrumbs = [], tabs = [], pending = false, error = null, children }) => {
  return (
    <Status pending={pending} error={error}>
      <div className='bg-white m-4 p-4 rounded shadow'>
        {title || breadcrumbs.length > 0 ? (
          <div className='flex justify-between border-b p-4'>
            <div className='flex flex-col space-y-2'>
              {breadcrumbs.length > 0 ? (
                <div className='flex items-center space-x-4 overflow-auto'>
                  <Breadcrumbs breadcrumbs={breadcrumbs} />
                  {pending && <Icon icon='refresh' className='animate-spin-slow text-sm' />}
                </div>
              ) : (
                <div className='text-xl font-bold'>{title}</div>
              )}
            </div>
            <div className='flex items-center space-x-2'>{buttons}</div>
          </div>
        ) : null}
        {tabs.length > 0 ? <Tabs list={tabs} /> : null}
        <ErrorBoundary FallbackComponent={ErrorFallback}>
          <div className='p-4'>{children}</div>
        </ErrorBoundary>
      </div>
    </Status>
  )
}

export default Page
