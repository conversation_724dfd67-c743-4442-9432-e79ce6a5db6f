import { useQuery, useQueryClient } from '@tanstack/react-query'
import { ButtonDropdown, Table, Tag } from '@bitcine/cinesend-theme'
import useStore from '../../hooks/use_store'
import { useNavigate, useParams } from 'react-router-dom'
import useTableFilters from '../../hooks/use_table_filters'

const Categories = ({ queryKey }) => {
  const params = useParams()
  const { deleteModel } = useStore()
  const queryClient = useQueryClient()
  const { data: airline, isLoading: isAirlineLoading, error: airlineError } = useQuery(['airlines/', params.airlineID])
  const langID = airline?.languages?.[0]?.id || null

  const filteringOptions = [
    {
      label: 'Type',
      key: 'type',
      type: 'checkboxes',
      options: [
        {
          label: 'Categories',
          key: 'categories',
          value: 'categories',
        },
        {
          label: 'Assets',
          key: 'assets',
          value: false,
        },
      ],
    },
  ]

  const { queryString, searching, sorting, filtering } = useTableFilters({
    filteringOptions,
    enableSearch: true,
    queryExists: true,
  })

  const { data: categories, isLoading, error } = useQuery([queryKey, queryString])

  const navigate = useNavigate()
  return (
    <Table
      status={{
        pending: isLoading || !airline,
        error: error || airlineError,
        errorMessage: error?.message || airlineError?.message,
      }}
      widths={[100, 85, 600, 100, 65]}
      header={{
        columns: [
          { text: 'Title', key: 'title' },
          { text: 'Type', key: 'type' },
          { text: 'Items', key: 'category_items' },
          { text: 'Count', key: 'count' },
          { text: '' },
        ],
        searching,
        sorting,
        filtering,
      }}
      body={{
        data: categories,
        row: {
          compact: true,
          spaced: true,
          langID: langID,
          onClick: (event, data) => navigate(`${data.id}/items`),
          render: [
            (data, index, { langID }) => data.category_values?.find((v) => v.language_id === langID)?.value || '',
            (data) => (
              <Tag
                label={
                  data.is_root ? 'Top Level' : data.type ? data.type.charAt(0).toUpperCase() + data.type.slice(1) : '-'
                }
                outline
              />
            ),
            (data, index, { langID }) => (
              <div className='mr-12'>
                {[
                  ...data.category_items,
                  ...data.child_categories.map((item) => item.category_values.find((v) => v.language_id === langID)),
                ]
                  .map((item) => item?.title || item?.value)
                  .join(' - ')}
              </div>
            ),
            (data) => data.category_items.length,
            (data) => (
              <ButtonDropdown
                kebab
                dropdown={{
                  content: [
                    {
                      text: 'Delete',
                      className: 'text-red-500',
                      onClick: () =>
                        deleteModel.mutate({
                          endpoint: 'categories',
                          id: data.id,
                          onSuccess: () => {
                            queryClient.invalidateQueries([queryKey])
                          },
                        }),
                    },
                  ],
                }}
              />
            ),
          ],
        },
        empty: {
          title: 'No categories found!',
          text: 'Create a new category to begin.',
          icon: 'category',
        },
      }}
    />
  )
}

export default Categories
