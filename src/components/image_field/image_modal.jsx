import { Modal } from '@bitcine/cinesend-theme/dist'
import Upload from './image_upload'
import Tabs from '../layouts/tabs'
import { useState } from 'react'
import Select from './image_select'
import AttachedImage from './attached_image'

export default function ImageModal({ assetID, assetImage, field, onClose }) {
  const [selectedTab, setSelectedTab] = useState('select')

  return (
    <Modal header={`Manage ${field.name}`} className='w-2/3' onClose={onClose}>
      {assetImage ? (
        <AttachedImage assetID={assetID} field={field} assetImage={assetImage} />
      ) : (
        <div className='space-y-4'>
          <Tabs
            wide
            list={[
              {
                name: 'Select from Uploads',
                selected: selectedTab === 'select',
                onClick: () => setSelectedTab('select'),
              },
              {
                name: 'Upload',
                selected: selectedTab === 'upload',
                onClick: () => setSelectedTab('upload'),
              },
            ]}
          />
          {selectedTab === 'select' ? (
            <Select assetID={assetID} field={field} />
          ) : selectedTab === 'upload' ? (
            <Upload assetImage={assetImage} assetID={assetID} field={field} />
          ) : null}
        </div>
      )}
    </Modal>
  )
}
