import { useQueryClient } from '@tanstack/react-query'
import useStore from '../../hooks/use_store'
import ImagesTable from '../images_table'
import { useParams } from 'react-router-dom'
import { Status } from '@bitcine/cinesend-theme/dist'

export default function Select({ assetID, field }) {
  const { updateModel } = useStore()
  const queryClient = useQueryClient()
  const { cycleID } = useParams()
  const saveImage = (assetImage) => {
    updateModel.mutate(
      {
        endpoint: '/asset-images',
        id: assetImage.id,
        data: {
          asset_id: assetID,
          field_id: field.id,
        },
      },
      {
        onSuccess: () => {
          updateModel.mutate(
            {
              endpoint: '/field-values',
              id: field.value.id,
              data: {
                cycle_id: cycleID,
                value: assetImage.file_name,
              },
            },
            {
              onSuccess: () => {
                queryClient.invalidateQueries([`fields`])
                queryClient.invalidateQueries([`field-values`])
              },
            }
          )
        },
      }
    )
  }
  return (
    <Status pending={updateModel.isLoading}>
      <ImagesTable type='unmatched' onClick={(assetImage) => saveImage(assetImage)} />
    </Status>
  )
}
