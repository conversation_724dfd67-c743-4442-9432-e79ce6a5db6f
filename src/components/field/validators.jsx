import { ButtonDropdown, Input, Table, Toggle } from '@bitcine/cinesend-theme'
import React, { useState } from 'react'
import useStore from '../../hooks/use_store'
import { useQuery } from '@tanstack/react-query'
import ValidatorParams from './validator_params'

const Validators = ({ fieldId, type }) => {
  const { createModel } = useStore()
  const { isLoading, error, data: validators } = useQuery([`validator-fields?type=${type}&field_id=${fieldId}`])

  const formatParameters = (data) => {
    if (!data.parameters || typeof data.parameters != 'object' || Object.keys(data.parameters).length === 0) {
      return ''
    }

    return <ValidatorParams key={`${data.name}-${data.id}`} name={data.name} data={data} fieldId={fieldId} />
  }

  const onStatusChange = (item) => {
    createModel.mutate({
      endpoint: 'validator-fields',
      data: {
        validator_id: item.id,
        field_id: fieldId,
        severity: item.severity,
        enabled: item.active,
        parameters: item.parameters,
      },
    })
  }

  const severityDropdown = (data) => {
    const content =
      validators?.severity_levels?.map((item) => ({
        text: item.label,
        onClick: () => {
          data.severity = item.value
          onStatusChange(data)
        },
      })) || []

    return (
      <ButtonDropdown
        button={{
          text: data.severity || 'N/A',
          link: true,
        }}
        dropdown={{
          content: content,
        }}
      />
    )
  }

  if (!validators?.severity_levels) {
    return <div>Loading...</div>
  }

  return (
    <div className='pt-5 '>
      <p>Field Validators:</p>
      <Table
        // if validator has a list of params we need it to be visible
        className='[&>tbody>tr]:!overflow-visible [&>tbody>tr>td]:!overflow-visible'
        status={{
          pending: isLoading,
          error: error,
          errorMessage: error?.message,
        }}
        widths={[100, 120, 180, 'auto', 100]}
        header={{
          columns: [
            { text: 'Active', key: 'active' },
            { text: 'Validator', key: 'validator' },
            { text: 'Value', key: 'parameters' },
            { text: 'Description', key: 'description' },
            { text: 'Severity', key: 'severity' },
          ],
          sorting: {},
          filters: {},
        }}
        body={{
          data: validators?.data,
          row: {
            spaced: true,
            compact: true,
            truncate: false,
            render: [
              (data) => (
                <Toggle
                  id={data.id}
                  key={data.id}
                  onChange={() => {
                    data.active = !data.active
                    onStatusChange(data)
                  }}
                  checked={data.active}
                />
              ),
              (data) => data.name,
              (data) => formatParameters(data),
              (data) => data.description,
              (data) => severityDropdown(data),
            ],
          },
          empty: {
            title: 'No validators found!',
            text: 'Create a new validator to begin.',
            icon: 'info',
          },
        }}
      />
    </div>
  )
}

export default Validators
