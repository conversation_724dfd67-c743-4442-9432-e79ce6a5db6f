import { SegmentedControl, Select } from '@bitcine/cinesend-theme/dist'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import useUtilities from '../../hooks/use_utilities'

const AssetTypes = ({ field, saveField }) => {
  const defaultAssetTypes = field.asset_types || []
  const [assetTypes, setAssetTypes] = useState(defaultAssetTypes)
  const [showAssetTypes, setShowAssetTypes] = useState(defaultAssetTypes.length > 0)
  const { assetTypes: availableAssetTypes } = useUtilities()
  const assetTypeOptions = availableAssetTypes?.map((opt) => ({ ...opt, label: opt.plural_label }))
  const update = (data) => {
    setAssetTypes(data)
    if (data.length === 0) {
      // data = ""
    }
    saveField({ asset_types: data })
  }
  return (
    <>
      <SegmentedControl
        label={'Applicable asset types'}
        options={[
          { value: 'all', label: 'All Asset Types' },
          { value: 'specified', label: 'Specified Asset Types' },
        ]}
        value={showAssetTypes ? 'specified' : 'all'}
        onChange={(opt) => {
          if (opt === 'all') {
            setShowAssetTypes(false)
            update([])
          } else {
            setShowAssetTypes(true)
          }
        }}
      />
      {showAssetTypes && (
        <Select
          options={assetTypeOptions}
          isMulti
          value={assetTypeOptions?.filter((opt) => assetTypes.includes(opt.value))}
          label={'Specified Asset Types'}
          description={'If this field only applies to a specific asset type, select it here. Otherwise, leave blank.'}
          onChange={(opt) => update(opt.map((opt) => opt.value))}
        />
      )}
    </>
  )
}

export default AssetTypes
