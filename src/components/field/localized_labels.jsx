import { useQuery } from '@tanstack/react-query'
import { Input, Status } from '@bitcine/cinesend-theme/dist'
import { useState } from 'react'
import useStore from '../../hooks/use_store'

const LocalizedLabels = ({ field, airlineID, cycleID }) => {
  const { data: labels, isLoading, isError, error } = useQuery([`field-labels`, `?fieldID=${field.id}`])
  const { data: airline } = useQuery([`airlines/${airlineID}`])

  if (!airline) {
    return null
  }

  return (
    <Status pending={isLoading} isError={isError} errorMessage={error?.message}>
      <div className='flex items-center space-x-4'>
        {airline?.languages?.map((language) => (
          <LocalizedLabel
            key={language.id}
            language={language}
            field={field}
            cycleID={cycleID}
            label={labels?.find((label) => language.id === label.language_id)}
          />
        ))}
      </div>
    </Status>
  )
}

const LocalizedLabel = ({ language, field, label, cycleID }) => {
  const [value, setValue] = useState(label?.value)
  const { updateModel, createModel } = useStore()
  const updateLabel = (newValue) => {
    if (label) {
      updateModel.mutate({
        endpoint: `field-labels`,
        id: label.id,
        data: {
          value: newValue,
        },
      })
    } else {
      createModel.mutate({
        endpoint: `field-labels`,
        data: {
          field_id: field.id,
          language_id: language.id,
          value: newValue,
          cycle_id: cycleID,
        },
      })
    }
  }
  return (
    <Input
      label={`${language?.eng_description} label`}
      placeholder='Enter localized label here'
      value={value}
      onChange={(e) => setValue(e.target.value)}
      onBlur={(e) => updateLabel(e.target.value)}
    />
  )
}

export default LocalizedLabels
