import { Toggle } from '@bitcine/cinesend-theme/dist'
import { useState } from 'react'

const IsLocalizable = ({ field, saveField }) => {
  const [isLocalizable, setIsLocalizable] = useState(field.is_localizable)
  return (
    <Toggle
      checked={isLocalizable}
      label='Are values localizable?'
      onChange={() => {
        setIsLocalizable(!isLocalizable)
        saveField({ is_localizable: !isLocalizable })
      }}
    />
  )
}

export default IsLocalizable
