import { Toggle } from '@bitcine/cinesend-theme/dist'
import { useState } from 'react'

const IsSizeField = ({ field, saveField }) => {
  const [isSizeField, setIsSizeField] = useState(field.is_size_field)
  return (
    <Toggle
      className='space-x-4'
      checked={isSizeField}
      label='Size field'
      onChange={() => {
        setIsSizeField(!isSizeField)
        saveField({ is_size_field: !isSizeField })
      }}
    />
  )
}

export default IsSizeField
