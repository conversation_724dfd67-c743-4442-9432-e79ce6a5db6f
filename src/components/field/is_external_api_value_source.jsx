import { Toggle } from '@bitcine/cinesend-theme/dist'
import { useState } from 'react'

const IsExternalApiValueSource = ({ field, saveField }) => {
  const [isExternalApiValueSource, setIsExternalApiValueSource] = useState(field.is_external_api_value_source)
  return (
    <Toggle
      className='space-x-4'
      checked={isExternalApiValueSource}
      label='Get true value via Viasat API'
      onChange={() => {
        setIsExternalApiValueSource(!isExternalApiValueSource)
        saveField({ is_external_api_value_source: !isExternalApiValueSource })
      }}
    />
  )
}

export default IsExternalApiValueSource
