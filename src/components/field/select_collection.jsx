import { SegmentedControl, Select } from '@bitcine/cinesend-theme/dist'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'

const SelectCollection = ({ schema, field, saveField }) => {
  const [collectionID, setCollectionID] = useState(field.collection_id)
  const [showCollections, setShowCollections] = useState(!field.use_languages)
  const { data: collections, isLoading } = useQuery([`collections?schemaID=${schema.id}`])
  const options = collections ? collections.map((collection) => ({ value: collection.id, label: collection.name })) : []
  const updateField = (collectionID) => {
    setCollectionID(collectionID)

    if (collectionID) {
      saveField({ collection_id: collectionID, use_languages: false })
    } else {
      saveField({ collection_id: null, use_languages: true })
    }
  }
  return (
    <div className='space-y-4'>
      <SegmentedControl
        label='List type'
        options={[
          { label: 'Custom List', value: 'list' },
          { label: 'Language List', value: 'language' },
        ]}
        value={!showCollections ? 'language' : 'list'}
        onChange={(opt) => {
          if (opt === 'language') {
            updateField(null)
            setShowCollections(false)
          } else {
            setShowCollections(true)
          }
        }}
      />
      {showCollections && (
        <Select
          label='List'
          isLoading={isLoading}
          options={options}
          value={options.find((opt) => opt.value === collectionID)}
          onChange={(opt) => updateField(opt.value)}
        />
      )}
    </div>
  )
}

export default SelectCollection
