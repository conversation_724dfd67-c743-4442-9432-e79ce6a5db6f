import { Select, Tag } from '@bitcine/cinesend-theme/dist'
import { useQuery } from '@tanstack/react-query'

const CollectionDropdown = ({ field, value, isLoading, languageID = null, saveField }) => {
  const { data, isLoading: isLoadingOptions } = useQuery(
    [
      `collection-values`,
      `?collectionID=${field.collection_id}`,
      languageID ? `&languageID=${languageID}` : null,
    ].filter((opt) => !!opt)
  )
  const options = data ? data.map((option) => ({ value: option.id, label: option.label })) : []
  return (
    <Select
      label={field.name}
      isLoading={isLoading || isLoadingOptions}
      value={
        field.is_multi_select
          ? value
            ? JSON.parse(value).map((val) => options.find((opt) => opt.value === val) || { value: val, label: val })
            : []
          : options.find((opt) => String(opt.value) === String(value)) || (value ? { value, label: value } : null)
      }
      options={options}
      isMulti={field.is_multi_select}
      onChange={(opt) => {
        if (field.is_multi_select) {
          saveField(JSON.stringify(opt.map((opt) => opt.value)))
        } else {
          saveField(opt.value)
        }
      }}
    />
  )
}

const LanguageDropdown = ({ field, value, isLoading, saveField }) => {
  const { data, isLoading: isLoadingOptions } = useQuery(['/api/languages'])
  const options = data ? data.map((option) => ({ value: option.id, label: option.eng_description })) : []
  return (
    <Select
      label={field.name}
      isLoading={isLoading || isLoadingOptions}
      isClearable
      value={options.find((opt) => (value ? opt.value == JSON.parse(value) : false))}
      options={options}
      onChange={(opt) => saveField(opt.value)}
    />
  )
}

const Dropdown = ({ field, ...props }) => {
  if (field.collection_id && !field.use_languages) {
    return <CollectionDropdown field={field} {...props} />
  } else if (field.use_languages) {
    return <LanguageDropdown field={field} {...props} />
  }
  return <Tag outline type='error' label='Undefined Dropdown' />
}

export default Dropdown
