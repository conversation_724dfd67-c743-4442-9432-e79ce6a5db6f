import { DatePicker, Input, Message, Toggle } from '@bitcine/cinesend-theme/dist'
import { useState } from 'react'
import useStore from '../../hooks/use_store'
import Dropdown from './dropdown'
import ImageField from '../image_field'
import FileSelector from './file_selector'
import { useNavigate, useParams } from 'react-router-dom'

const FieldValue = ({
  className = '',
  field,
  assetID,
  cycleID,
  languageID = null,
  isLoading,
  disabled,
  refreshValues,
}) => {
  const { updateModel, createModel } = useStore()
  const fieldValue = field.value
  const fieldType = field.field_type
  const [value, setValue] = useState(fieldValue?.value ?? fieldValue?.file_name ?? '')
  const params = useParams()
  const navigate = useNavigate()
  const isDisabled = disabled || field?.is_external_api_value_source
  const [message, setMessage] = useState(
    field?.value?.issues?.[0]?.description || field?.assetImage?.issues?.[0]?.description || ''
  )

  const isEmpty = (val) => {
    return val === null || val === undefined || val === ''
  }

  const saveField = (newValue) => {
    // save only if value has changed
    if ((isEmpty(fieldValue?.value) && isEmpty(newValue)) || fieldValue?.value === newValue) {
      return
    }

    setValue(newValue)

    // If the field value already exists, we're just updating the value. Otherwise, we create it.
    if (fieldValue) {
      updateModel.mutate({
        endpoint: 'field-values',
        id: fieldValue.id,
        data: {
          value: newValue,
          cycle_id: cycleID,
          field_id: field.id,
        },
        onSuccess: (res) => {
          setMessage(null)
          setValue(res?.data?.field?.value)
          // update asset id if changed
          if (res.data.field.asset_id !== assetID) {
            navigate(
              `/airlines/${params.airlineID}/cycles/${cycleID}/metadata/${params.assetType}/${res.data.field.asset_id}/details/${params['*']}`
            )
          }
        },
        onError: (error) => setMessage(error.response.data.message),
      })
    } else {
      let data = {
        field_id: field.id,
        asset_id: parseInt(assetID),
        value: newValue,
        cycle_id: cycleID,
      }

      // Do not send language_id if this is a dropdown
      if (fieldType !== 'dropdown') {
        data['language_id'] = languageID
      }

      createModel.mutate({
        endpoint: 'field-values',
        data,
      })
    }
  }

  return (
    <div className={`${className} w-full`}>
      {fieldType === 'string' && (
        <div className='flex items-end space-x-2'>
          <Input
            label={field.name}
            value={value}
            disabled={isDisabled}
            onChange={(e) => setValue(e.target.value)}
            onBlur={(e) => saveField(e.target.value)}
            error={!!message}
            id={`field-value-${field.value?.id}`}
          />
        </div>
      )}
      {['video', 'audio'].includes(fieldType) && (
        <div className='flex items-end space-x-2'>
          <Input
            label={field.name}
            value={value}
            disabled={isDisabled}
            onChange={(e) => setValue(e.target.value)}
            onBlur={(e) => saveField(e.target.value)}
            error={!!message}
            id={`field-value-${field.value?.id}`}
          />
          <FileSelector
            cycleID={cycleID}
            assetID={assetID}
            value={value}
            field={field}
            isLoading={isLoading}
            saveField={refreshValues}
          />
        </div>
      )}
      {fieldType === 'datetime' && (
        <DatePicker
          label={field.name}
          className='w-full'
          date={value}
          disabled={isDisabled}
          showTimeSelect={false}
          onChange={(newDate) => saveField(newDate)}
          error={!!message}
        />
      )}
      {fieldType === 'dropdown' && (
        <Dropdown
          field={field}
          value={value}
          disabled={isDisabled}
          isLoading={isLoading}
          languageID={languageID}
          saveField={saveField}
          error={!!message}
        />
      )}
      {fieldType === 'image' && (
        <ImageField
          assetID={assetID}
          field={field}
          value={value}
          message={message}
          saveField={saveField}
          disabled={isDisabled}
        />
      )}
      {fieldType === 'boolean' && (
        <Toggle
          label={field.name}
          checked={value === 'true'}
          onChange={() => saveField(value === 'true' ? 'false' : 'true')}
        />
      )}
      {message && (
        <Message error={true} small={true}>
          {message}
        </Message>
      )}
    </div>
  )
}

export default FieldValue
