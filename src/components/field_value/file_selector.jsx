import { But<PERSON>, Modal, Select } from '@bitcine/cinesend-theme/dist'
import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import useStore from '../../hooks/use_store'

function OpenModal({ cycleID, assetID, field, isLoading, value, onClose, saveField }) {
  const [stateValue, setStateValue] = useState(value)
  const { data, isLoading: isLoadingOptions } = useQuery([`media-info-files?assetID=${assetID}`])
  const options = data ? data.map((option) => ({ value: option.file_name, label: option.file_name })) : []
  const { updateModel, createModel } = useStore()
  const navigate = useNavigate()
  const params = useParams()

  const saveFileValue = (newValue) => {
    let data = {
      cycle_id: cycleID,
      asset_id: parseInt(assetID),
      value: newValue,
    }

    if (field.field_type === 'video') {
      saveVideoValue(data)
    } else if (field.field_type === 'audio') {
      saveAudioValue(data)
    } else {
      setMessage('Invalid field type')
    }
  }

  const saveVideoValue = (data) => {
    if (field.value?.id) {
      updateModel.mutate({ endpoint: 'asset-videos', id: field.value.id, data, onSuccess: onSuccessHandler })
    } else {
      data.field_id = field.id
      createModel.mutate({ endpoint: 'asset-videos', data, onSuccess: onSuccessHandler })
    }
  }

  const saveAudioValue = (data) => {
    if (value) {
      updateModel.mutate({ endpoint: 'asset-audios', id: field.value.id, data, onSuccess: onSuccessHandler })
    } else {
      data.field_id = field.id
      createModel.mutate({ endpoint: 'asset-audios', data, onSuccess: onSuccessHandler })
    }
  }

  const onSuccessHandler = (res) => {
    saveField()

    if (res.data.field.asset_id !== assetID) {
      navigate(
        `/airlines/${params.airlineID}/cycles/${cycleID}/metadata/${params.assetType}/${res.data.field.asset_id}/details/${params['*']}`
      )
    }
  }

  return (
    <Modal
      header={'Select File from Viasat API'}
      onClose={onClose}
      confirmButton={{
        text: 'Save',
        onClick: () => {
          saveFileValue(stateValue)
          onClose()
        },
      }}
    >
      <Select
        label={field.name}
        isLoading={isLoading || isLoadingOptions}
        value={options.find((opt) => opt.value === stateValue)}
        options={options}
        isMulti={field.is_multi_select}
        onChange={(opt) => setStateValue(opt.value)}
      />
    </Modal>
  )
}

export default function FileSelector({ cycleID, assetID, field, value, isLoading, saveField }) {
  const [modalOpen, setModalOpen] = useState(false)

  return (
    <div>
      <Button className='min-w-40' secondary onClick={() => setModalOpen(true)}>
        Select File
      </Button>
      {modalOpen && (
        <OpenModal
          cycleID={cycleID}
          assetID={assetID}
          field={field}
          value={value}
          isLoading={isLoading}
          saveField={saveField}
          onClose={() => setModalOpen(false)}
        />
      )}
    </div>
  )
}
