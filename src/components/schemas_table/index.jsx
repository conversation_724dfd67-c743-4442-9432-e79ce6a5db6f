import { useQuery } from '@tanstack/react-query'
import { ButtonDropdown, Table } from '@bitcine/cinesend-theme'
import useStore from '../../hooks/use_store'
import { useNavigate } from 'react-router-dom'
import useTableFilters from '../../hooks/use_table_filters'

const SchemasTable = ({ queryKey }) => {
  const { deleteModel } = useStore()
  const { queryString, searching } = useTableFilters({
    enableSearch: true,
  })
  const { data: schemas, error, isLoading } = useQuery([queryKey, queryString])
  const navigate = useNavigate()

  return (
    <Table
      status={{
        pending: isLoading,
        error: error,
        errorMessage: error?.message,
      }}
      widths={[200, 200, 200, 65]}
      header={{
        columns: [
          { text: 'Name', key: 'name' },
          { text: 'Airline', key: 'airline_name' },
          { text: 'Version', key: 'version_number' },
          { text: '' },
        ],
        searching,
      }}
      body={{
        data: schemas,
        row: {
          compact: true,
          spaced: true,
          onClick: (event, data) => navigate(`/settings/schemas/${data.id}`),
          render: [
            (data) => data.name,
            (data) => data.airline?.name,
            (data) => data.version_number,
            (data) => (
              <ButtonDropdown
                kebab
                dropdown={{
                  content: [
                    {
                      text: 'Delete',
                      className: 'text-red-500',
                      onClick: () => deleteModel.mutate({ endpoint: 'schemas', id: data.id }),
                    },
                  ],
                }}
              />
            ),
          ],
        },
        empty: {
          title: 'No schemas found!',
          text: 'Clone an existing schema to begin.',
          icon: 'list_alt',
        },
      }}
    />
  )
}

export default SchemasTable
