import { useParams } from 'react-router-dom'
import { useQuery } from '@tanstack/react-query'
import { Button, Status } from '@bitcine/cinesend-theme/dist'
import LocalizableLabels from './localizable_labels'
import Details from './details'
import CommonValues from './common_values'
import useStore from '../../hooks/use_store'

const List = ({ schema }) => {
  const params = useParams()
  const { data: collection, isLoading, isError, error } = useQuery(['collections/', params.listID])
  const { createModel } = useStore()
  return (
    <Status pending={isLoading} error={isError} errorMessage={error?.message}>
      <div className='border rounded p-6'>
        <Details collection={collection} />
        <div className='font-semibold mb-4'>Items</div>
        {collection?.is_localizable ? (
          <LocalizableLabels collection={collection} schema={schema} />
        ) : (
          <CommonValues collection={collection} />
        )}
        <div className='flex justify-end my-4'>
          <Button
            icon='add_circle_outline'
            onClick={() => {
              createModel.mutate({ endpoint: 'collection-values', data: { collection_id: collection.id } })
            }}
          >
            Add New Item
          </Button>
        </div>
      </div>
    </Status>
  )
}

export default List
