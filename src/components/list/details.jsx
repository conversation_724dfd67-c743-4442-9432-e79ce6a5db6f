import { Button, Input, Toggle } from '@bitcine/cinesend-theme/dist'
import useStore from '../../hooks/use_store'
import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

const Details = ({ collection }) => {
  const [state, setState] = useState(collection)
  const { updateModel } = useStore()
  const navigate = useNavigate()
  const update = (data) => {
    setState({ ...state, ...data })
    updateModel.mutate({
      endpoint: `collections`,
      id: collection.id,
      data,
    })
  }
  const { deleteModel } = useStore()
  const deleteList = () => {
    deleteModel.mutate(
      {
        endpoint: 'collections',
        id: collection.id,
      },
      {
        onSuccess: () => navigate(`/airlines/${collection.airline_id}/lists`),
      }
    )
  }
  return (
    <div className='border-b mb-4 pb-4 space-y-4'>
      <div className='flex justify-between border-b pb-4 mb-4'>
        <div className='font-semibold text-lg'>{collection?.name}</div>
        <Button type='error' size='small' secondary icon={'delete_forever'} onClick={() => deleteList()}>
          Delete List
        </Button>
      </div>
      <div className='space-y-4'>
        <div className='font-semibold'>Details</div>
        <Input
          label='Name'
          value={state.name}
          onChange={(e) => setState({ ...state, name: e.target.value })}
          onBlur={(e) => update({ name: e.target.value })}
        />
        <Toggle
          label='Is Localizable'
          checked={state.is_localizable}
          onChange={(e) => update({ is_localizable: e.target.checked })}
        />
      </div>
    </div>
  )
}

export default Details
