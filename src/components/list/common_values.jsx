import { useQuery } from '@tanstack/react-query'
import { Button, Table } from '@bitcine/cinesend-theme/dist'
import useStore from '../../hooks/use_store'
import CollectionValue from '../collection_value'

const CommonValues = ({ collection }) => {
  const { data: collectionValues, isLoading, error } = useQuery([`collection-values`, `?collectionID=${collection.id}`])
  const { deleteModel } = useStore()

  return (
    <Table
      status={{
        pending: isLoading,
        error: error,
        errorMessage: error?.message,
      }}
      widths={['auto', 100]}
      header={{
        columns: [{ text: 'Values', key: 'name' }, { text: '' }],
      }}
      body={{
        data: collectionValues,
        row: {
          render: [
            (data) => <CollectionValue collection={collection} collectionValue={data} />,
            (data) => (
              <Button
                icon='remove_circle_outline'
                secondary
                type='error'
                onClick={() => {
                  deleteModel.mutate({ endpoint: 'collection-values', id: data.id })
                }}
              />
            ),
          ],
        },
        empty: {
          title: 'No items found!',
          text: 'Create a new item to begin.',
          icon: 'add_circle_outline',
        },
      }}
    />
  )
}

export default CommonValues
