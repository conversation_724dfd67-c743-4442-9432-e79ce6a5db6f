import { useQuery } from '@tanstack/react-query'
import { Button, Table } from '@bitcine/cinesend-theme/dist'
import useStore from '../../hooks/use_store'
import CollectionValue from '../collection_value'

const LocalizedLabels = ({ collection, schema }) => {
  const { data: airline } = useQuery([`airlines/${schema.airline_id}`])
  const { data: collectionValues, isLoading, error } = useQuery([`collection-values`, `?collectionID=${collection.id}`])

  const { deleteModel } = useStore()

  if (!airline || isLoading) {
    return null
  }

  const languages = airline.languages

  const nonLocalizedValues = collectionValues?.filter((opt) => !opt.language_id)
  const localizedValues = collectionValues?.filter((opt) => opt.language_id !== null)

  return (
    <Table
      status={{
        pending: isLoading,
        error: error,
        errorMessage: error?.message,
      }}
      widths={[200, ...languages.map(() => 200), 80]}
      header={{
        columns: [
          { text: 'Identifier label' },
          ...languages.map((opt) => ({ text: `${opt.eng_description} label`, key: opt.ife_code })),
          { text: '' },
        ],
      }}
      body={{
        data: nonLocalizedValues,
        row: {
          render: [
            (data) => <CollectionValue collection={collection} collectionValue={data} />,
            ...languages.map((language) => {
              const RenderCollectionValue = (data, index) => {
                const languageSpecificValue = localizedValues?.find(
                  (v) => v.language_id === language.id && v.collection_value_id === data.id
                )
                return (
                  <CollectionValue
                    collection={collection}
                    key={index}
                    identifierCollectionValue={data}
                    collectionValue={languageSpecificValue}
                    language={language}
                  />
                )
              }
              return RenderCollectionValue
            }),
            (data) => (
              <Button
                icon='remove_circle_outline'
                secondary
                type='error'
                onClick={() => {
                  deleteModel.mutate({ endpoint: 'collection-values', id: data.id })
                }}
              />
            ),
          ],
        },
        empty: {
          title: 'No items found!',
          text: 'Create a new item to begin.',
          icon: 'add_circle_outline',
        },
      }}
    />
  )
}

export default LocalizedLabels
