import { useQuery } from '@tanstack/react-query'
import { ButtonDropdown, Table, Tag } from '@bitcine/cinesend-theme'
import useStore from '../../hooks/use_store'
import { useParams } from 'react-router-dom'
import { humanFileSize } from '../../helpers/human_file_size'
import { convertToShorthandWithTime } from '../../helpers/convert_date'
import useTableFilters from '../../hooks/use_table_filters'
import ImageModal from '../../pages/images/image_modal'
import { useState } from 'react'

const sortingOptions = [
  { label: 'File name', key: 'file_name' },
  { label: 'File size', key: 'size' },
  { label: 'Created', key: 'created_at' },
]

const ImagesTable = ({ type, onClick }) => {
  const { queryString, searching, sorting, filtering, pagination } = useTableFilters({
    sortingOptions,
    enableSearch: true,
  })
  const [showImage, setShowImage] = useState(null)
  const { deleteModel } = useStore()
  const cycleID = useParams().cycleID
  const airlineID = useParams().airlineID
  const {
    data: imagesData,
    error,
    isLoading,
  } = useQuery([`/asset-images`, `${queryString}&type=${type}&airlineID=${airlineID}&cycleID=${cycleID}`])
  return (
    <>
      <Table
        status={{
          pending: isLoading,
          error: error,
          errorMessage: error?.message,
        }}
        widths={[300, 100, 90, 100, 120, 65]}
        header={{
          columns: [
            { text: 'File name', key: 'file_name' },
            { text: 'Asset' },
            { text: 'File size', key: 'size' },
            { text: 'Status', key: '' },
            { text: 'Created', key: 'created_at' },
            { text: '' },
          ],
          searching,
          sorting,
          filtering,
        }}
        body={{
          data: imagesData?.data?.data,
          row: {
            compact: true,
            spaced: true,
            onClick: (event, data) => (typeof onClick === 'function' ? onClick(data) : setShowImage(data)),
            render: [
              (data) => data.file_name,
              (data) => data.asset?.title,
              (data) => humanFileSize(data.size),
              (data) => (
                <Tag
                  outline
                  type={data.asset_id && data.field_id ? 'success' : 'warning'}
                  label={data.asset_id && data.field_id ? 'MATCHED' : 'UNMATCHED'}
                />
              ),
              (data) => convertToShorthandWithTime(data.created_at),
              (data) => (
                <ButtonDropdown
                  kebab
                  dropdown={{
                    content: [
                      {
                        text: 'Delete',
                        className: 'text-red-500',
                        icon: 'delete_forever',
                        onClick: () => deleteModel.mutate({ endpoint: '/asset-images', id: data.id }),
                      },
                    ],
                  }}
                />
              ),
            ],
          },
          empty: {
            title: 'No images found!',
            text: 'Upload new images to begin.',
            icon: 'image',
          },
        }}
        paginate={{
          ...pagination,
          totalRows: imagesData?.data?.total,
          currentPage: imagesData?.data?.current_page,
          rowsPerPage: imagesData?.data?.per_page,
        }}
      />
      {showImage && <ImageModal image={showImage} onClose={() => setShowImage(null)} />}
    </>
  )
}

export default ImagesTable
