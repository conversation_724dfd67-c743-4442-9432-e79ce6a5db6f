import { useState } from 'react'
import { Table, Tag, ButtonDropdown, Button } from '@bitcine/cinesend-theme'
import { useQueryClient } from '@tanstack/react-query'
import { useNavigate } from 'react-router-dom'
import useStore from '../../hooks/use_store'
import { statusTypes } from '../../constants/status_colour_map'
import CompletionLabel from '../completion_label'

const AssetsCollectionTable = ({ cycle, assetId, assetType, childAssets }) => {
  const cycleID = cycle.id
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const [search, setSearch] = useState('')

  const { createModel, deleteModel } = useStore()
  const goToAsset = (data) => navigate(`${data.id}`)

  const createAsset = () => {
    createModel.mutate(
      {
        endpoint: `cycles/${cycleID}/assets`,
        data: {
          cycle_id: cycleID,
          title: `New ${assetType?.label}`,
          asset_type: assetType.value,
          parent_asset_id: assetId,
        },
      },
      {
        onSuccess: (res) => {
          if (res.data.asset) {
            goToAsset(res.data.asset)
          }
        },
      }
    )
  }
  const showValidatorsResult = (data) => {
    return (
      <div className='flex flex-wrap gap-1'>
        {data.errors > 0 && <Tag label={`Errors: ${data.errors}`} type='error' outline={true} />}
        {data.warnings > 0 && <Tag label={`Warnings: ${data.warnings}`} type='warning' outline={true} />}
        {!data.warnings && !data.errors && <Tag label='OK' type='success' outline={true} />}
      </div>
    )
  }
  return (
    <>
      <Table
        status={{
          pending: deleteModel.isLoading,
        }}
        widths={[200, 200, 200, 100, 65]}
        header={{
          columns: [
            { text: 'Asset Title', key: 'asset_title' },
            { text: 'Completion', key: 'completion_percentage' },
            { text: 'Validators', key: '' },
            { text: 'Type', key: 'status' },
            { text: '' },
          ],
          searching: {
            searchPlaceholder: 'Enter a search term here...',
            search,
            onSearch: (term) => setSearch(term),
          },
          customElement: (
            <Button icon='add_circle_outline' onClick={() => createAsset()}>
              Create {assetType?.label}
            </Button>
          ),
        }}
        body={{
          data: childAssets,
          row: {
            compact: true,
            spaced: true,
            onClick: (event, data) => goToAsset(data),
            render: [
              (data) => data.default_title,
              (data) => <CompletionLabel percentage={data.completion_percentage} />,
              (data) => showValidatorsResult(data),
              (data) => <Tag outline type={statusTypes[data?.status?.toLowerCase()]} label={data.status ?? 'N/A'} />,
              (data) => (
                <ButtonDropdown
                  kebab
                  dropdown={{
                    content: [
                      {
                        text: 'Delete',
                        className: 'text-red-500',
                        onClick: () =>
                          deleteModel.mutate({
                            endpoint: `cycles/${cycleID}/assets`,
                            id: data.id,
                            onSuccess: () => {
                              queryClient.invalidateQueries(`cycles/${cycleID}/assets/${data.parent_asset_id}`)
                            },
                          }),
                      },
                    ],
                  }}
                />
              ),
            ],
          },
          empty: {
            title: `No ${assetType?.plural_label?.toLowerCase()} found!`,
            text: `Add ${assetType?.label?.toLowerCase()} to the asset to begin.`,
            icon: 'article',
          },
        }}
      />
    </>
  )
}

export default AssetsCollectionTable
