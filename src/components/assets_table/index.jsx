import { useEffect, useState } from 'react'
import useStore from '../../hooks/use_store'
import { statusTypes } from '../../constants/status_colour_map'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Button, Table, Tag, ButtonDropdown } from '@bitcine/cinesend-theme'
import { useNavigate } from 'react-router-dom'
import axios from 'axios'
import openInNewTab from '../../helpers/openinnewtab'
import RecallModal from './recall_modal'
import ImportForm from './import_form'
import CompletionLabel from '../completion_label'
import Page from '../layouts/page'
import useUtilities from '../../hooks/use_utilities'
import CreateAssetButton from './create_asset_button'
import useTableFilters from '../../hooks/use_table_filters'

const AssetsTable = ({
  cycle,
  assetType: { value: assetTypeValue, label: assetLabel, plural_label: assetLabelPlural },
}) => {
  const cycleID = cycle.id
  const navigate = useNavigate()
  const { assetTypes } = useUtilities()
  const queryClient = useQueryClient()
  const { createModel, deleteModel } = useStore()
  const [recallModal, setRecallModal] = useState(false)
  const queryKey = `cycles/${cycleID}/assets?assetType=${assetTypeValue}`
  const { data: recallableAssets } = useQuery([`cycles/${cycle.id}/recallable-assets?assetType=${assetTypeValue}`])
  const [isModalOpen, setIsModalOpen] = useState(false)

  const [initialPagination, setInitialPagination] = useState()

  const filteringOptions = [
    {
      label: 'Type',
      key: 'type',
      type: 'checkboxes',
      options: [
        {
          label: 'New',
          key: 'new',
          value: 'new',
        },
        {
          label: 'Recalled',
          key: 'recalled',
          value: 'recalled',
        },
        {
          label: 'Expiring',
          key: 'expiring',
          value: 'expiring',
        },
        {
          label: 'Holdover',
          key: 'holdover',
          value: 'holdover',
        },
      ],
    },
  ]

  const { queryString, searching, sorting, filtering, pagination } = useTableFilters({
    filteringOptions,
    enableSearch: true,
    queryExists: true,
    initialPagination: { ...initialPagination },
  })
  const { data, isLoading, isError, error } = useQuery([queryKey, queryString])

  useEffect(() => {
    if (data?.meta) {
      setInitialPagination({
        currentPage: data.meta.current_page - 1, // starts from 1 on backend and from 0 in here
        rowsPerPage: data.meta.per_page,
        totalRows: data.meta.total,
      })
    }
  }, [data])

  const openModal = () => setIsModalOpen(true)
  const closeModal = () => {
    queryClient.invalidateQueries(queryKey)
    setIsModalOpen(false)
  }
  const goToAsset = (data) => navigate(`${data.id}`)

  const exportAssets = ({ isTemplate }) => {
    createModel.mutate(
      {
        endpoint: 'exports',
        data: {
          name: `export-cycle-${cycleID}-assetType-${assetTypeValue}`,
          cycleID: cycleID,
          type: assetTypeValue,
          isTemplate,
        },
      },
      {
        onSuccess: (res) => {
          if (res.data) {
            axios.get(`${process.env.VITE_API_URL}/exports/${res.data.export.id}/download`).then((r) => {
              openInNewTab(r.data.url)
            })
          }
        },
      }
    )
  }

  const showValidatorsResult = (data) => {
    return (
      <div className='flex flex-wrap gap-1'>
        {data.errors > 0 && <Tag label={`Errors: ${data.errors}`} type='error' outline={true} />}
        {data.warnings > 0 && <Tag label={`Warnings: ${data.warnings}`} type='warning' outline={true} />}
        {!data.warnings && !data.errors && <Tag label='OK' type='success' outline={true} />}
      </div>
    )
  }

  return (
    <Page
      title={assetLabelPlural}
      buttons={[
        <div key={'asset-buttons'} className='flex items-center space-x-2'>
          <CreateAssetButton cycleID={cycleID} assetType={assetTypeValue} assetLabel={assetLabel} />
          <ButtonDropdown
            button={{
              text: 'Export',
              icon: 'cloud_download',
            }}
            dropdown={{
              content: [
                {
                  text: 'Export Template',
                  onClick: () => exportAssets({ isTemplate: true }),
                },
                {
                  text: 'Export Populated',
                  onClick: () => exportAssets({ isTemplate: false }),
                },
              ],
            }}
          />

          <Button icon='cloud_upload' onClick={openModal}>
            {`Import ${assetTypeValue === 'series' ? 'TV Episodes' : assetLabelPlural}`}
          </Button>
          {recallableAssets?.length > 0 && (
            <Button icon='refresh' onClick={() => setRecallModal(true)}>
              Recall {assetLabelPlural}
            </Button>
          )}
        </div>,
      ]}
    >
      <Table
        status={{
          pending: isLoading || createModel.isLoading,
          error: isError,
          errorMessage: error?.message,
        }}
        widths={[200, 200, 200, 100, 55]}
        header={{
          columns: [
            { text: 'Asset Title', key: 'asset_title' },
            { text: 'Completion', key: 'completion_percentage' },
            { text: 'Validators', key: '' },
            { text: 'Status', key: 'status' },
            { text: '' },
          ],
          searching,
          sorting,
          filtering,
        }}
        body={{
          data: data?.data,
          row: {
            compact: true,
            spaced: true,
            onClick: (event, data) => goToAsset(data),
            render: [
              (data) => data.default_title,
              (data) => <CompletionLabel percentage={data.completion_percentage} />,
              (data) => showValidatorsResult(data),
              (data) => <Tag outline type={statusTypes[data?.status.toLowerCase()]} label={data.status ?? 'N/A'} />,
              (data) => (
                <ButtonDropdown
                  kebab
                  dropdown={{
                    content: [
                      {
                        text: 'Delete',
                        className: 'text-red-500',
                        onClick: () =>
                          deleteModel.mutate({
                            endpoint: `cycles/${cycleID}/assets`,
                            id: data.id,
                            confirmMessage: `Are you sure you want to delete ${data.title}?${
                              assetTypes.some((type) => type.value === data.asset_type && type.is_parent)
                                ? ' Deleting this asset will also delete all child assets.'
                                : ''
                            }`,
                          }),
                      },
                    ],
                  }}
                />
              ),
            ],
          },
          empty: {
            title: `No ${assetLabelPlural.toLowerCase()} found!`,
            text: `Create a new ${assetLabel.toLowerCase()} to begin.`,
            icon: 'article',
          },
        }}
        paginate={pagination}
      />
      {isModalOpen && (
        <ImportForm isOpen={isModalOpen} onClose={closeModal} cycleID={cycleID} assetType={assetTypeValue} />
      )}
      {recallModal && (
        <RecallModal
          queryKey={queryKey}
          id={cycleID}
          label={assetLabelPlural}
          onClose={() => {
            setRecallModal(false)
            queryClient.invalidateQueries(queryKey)
          }}
          list={recallableAssets}
        />
      )}
    </Page>
  )
}

export default AssetsTable
