import { useState } from 'react'
import { Button, Input, Modal } from '@bitcine/cinesend-theme'
import useStore from '../../hooks/use_store'
import { useNavigate } from 'react-router-dom'

export default function CreateAssetButton({ cycleID, assetType, assetLabel }) {
  const [modalOpen, setModalOpen] = useState(false)
  const [title, setTitle] = useState()
  const { createModel } = useStore()
  const navigate = useNavigate()

  const createAsset = () => {
    createModel.mutate(
      {
        endpoint: `cycles/${cycleID}/assets`,
        data: {
          cycle_id: cycleID,
          title,
          asset_type: assetType,
        },
      },
      {
        onSuccess: (res) => {
          setTitle('')
          setModalOpen(false)
          if (res.data.asset) {
            navigate(`${res.data.asset.id}`)
          }
        },
      }
    )
  }

  return (
    <>
      <Button onClick={() => setModalOpen(true)} icon='add_circle_outline'>
        Create
      </Button>
      {modalOpen && (
        <Modal
          header={`Create ${assetLabel}`}
          onClose={() => setModalOpen(false)}
          confirmButton={{
            text: `Create ${assetLabel}`,
            onClick: () => {
              createAsset()
            },
            disabled: !title,
          }}
        >
          <div>
            <Input
              label={`Title`}
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder={`Enter new title here...`}
            />
          </div>
        </Modal>
      )}
    </>
  )
}
