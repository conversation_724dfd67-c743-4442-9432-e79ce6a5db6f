import { Modal, Table } from '@bitcine/cinesend-theme/dist'
import { useMutation } from '@tanstack/react-query'
import { useState } from 'react'
import axios from 'axios'
import { useQueryClient } from '@tanstack/react-query'

const RecallModal = ({ label, onClose, list, id, queryKey }) => {
  const [selectedIDs, setSelectedIDs] = useState([])
  const queryClient = useQueryClient()
  const recallAsset = useMutation(
    () => {
      return axios.post(`/cycles/${id}/recall-asset`, { recalled_assets: selectedIDs })
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(queryKey)
        onClose()
      },
    }
  )
  return (
    <Modal
      header={`Recall ${label}`}
      onClose={onClose}
      confirmButton={{
        text: 'Recall',
        disabled: selectedIDs.length === 0,
        onClick: () => recallAsset.mutate(),
      }}
    >
      <Table
        widths={['auto']}
        body={{
          data: list,
          row: {
            checkbox: {
              checked: (data) => selectedIDs.includes(data.latest_version_id),
              onChange: (data) =>
                selectedIDs.includes(data.latest_version_id)
                  ? setSelectedIDs(selectedIDs.filter((i) => i !== data.latest_version_id))
                  : setSelectedIDs([...selectedIDs, data.latest_version_id]),
            },
            render: [(data) => data.title],
          },
        }}
      />
    </Modal>
  )
}

export default RecallModal
