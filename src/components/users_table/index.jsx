import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Table, ButtonDropdown } from '@bitcine/cinesend-theme'
import { useNavigate } from 'react-router-dom'
import useTableFilters from '../../hooks/use_table_filters'
import useStore from '../../hooks/use_store'

const UsersTable = ({ queryKey }) => {
  const { queryString, searching } = useTableFilters({
    enableSearch: true,
  })
  const { data, error, isLoading } = useQuery([queryKey, queryString])
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const { deleteModel } = useStore()

  const users = data?.data || []

  return (
    <Table
      status={{
        pending: isLoading,
        error: error,
        errorMessage: error?.message,
      }}
      widths={['auto', 300, 200, 200, 50]}
      header={{
        columns: [
          { text: 'Name', key: 'name' },
          { text: 'Email', key: 'email' },
          { text: 'Organization', key: 'organization' },
          { text: 'Role' },
          { text: '' },
        ],
        searching,
      }}
      body={{
        data: users,
        row: {
          compact: true,
          spaced: true,
          onClick: (event, data) => navigate(`/settings/users/${data.id}`),
          render: [
            (data) => data.name,
            (data) => data.email,
            (data) => data.organization?.name || 'N/A',
            (data) => data.role?.label || 'N/A',
            (data) => (
              <ButtonDropdown
                kebab
                dropdown={{
                  content: [
                    {
                      text: 'Delete User',
                      className: 'text-red-500',
                      icon: 'delete',
                      onClick: () =>
                        deleteModel.mutate({
                          endpoint: `users`,
                          id: data.id,
                          onSuccess: () => queryClient.invalidateQueries([queryKey, queryString]),
                        }),
                    },
                  ],
                }}
              />
            ),
          ],
        },
        empty: {
          title: 'No users found!',
          text: 'Create a user to begin.',
          icon: 'person',
        },
      }}
    />
  )
}

export default UsersTable
