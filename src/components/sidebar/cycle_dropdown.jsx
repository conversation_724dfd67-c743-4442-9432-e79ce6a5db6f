import { ButtonDropdown, Icon } from '@bitcine/cinesend-theme/dist'
import { useQuery } from '@tanstack/react-query'
import { useEffect } from 'react'

const CycleDropdown = ({ airlineID, cycleID, setCycleID }) => {
  const { data: cycles, isLoading } = useQuery(['cycles', `?airlineID=${airlineID}`])
  const cycle = cycles?.find((cycle) => cycle.id === cycleID)
  useEffect(() => {
    if (cycles && cycles.length > 0 && !cycles.map((opt) => opt.id).includes(cycleID)) {
      const newCycleID = cycles[0].id
      setCycleID(newCycleID)
    }
  }, [cycles, cycleID, setCycleID])
  if (isLoading) {
    return null
  }
  return (
    <ButtonDropdown
      button={{
        component: (
          <div
            className='w-56 justify-between space-x-4 text-xs flex items-center rounded bg-primary-700 hover:bg-primary-600 px-4'>
            <Icon icon='date_range' className='text-xl text-white flex-none'/>
            <span className='text-xs text-gray-200'>
              {cycle ? cycle.description : 'Select cycle'}
            </span>
            <Icon icon='arrow_drop_down' className='text-xl text-white flex-none' />
          </div>
        ),
      }}
      dropdown={{
        content: cycles?.map((cycle) => ({
          icon: cycle.id === cycleID ? 'check_circle' : '',
          className: cycle.id === cycleID ? 'text-green-600' : 'text-gray-500',
          text: cycle.description,
          onClick: () => setCycleID(cycle.id),
        })),
      }}
    />
  )
}

export default CycleDropdown
