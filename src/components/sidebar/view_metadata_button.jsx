import { Button } from '@bitcine/cinesend-theme/dist'
import { useNavigate } from 'react-router-dom'

const ViewMetadataButton = () => {
  const navigate = useNavigate()

  const goToMetadata = () => {
    let pathname = localStorage.getItem('backToMetadata')
    localStorage.clear('backToMetadata')

    if (!pathname) {
      pathname = '/dashboard'
    }
    navigate(pathname)
  }

  return (
    <div className='flex items-center justify-center mb-4 border-y border-gray-600 py-4'>
      <Button size='small' type='neutral' secondary onClick={() => goToMetadata()} icon='arrow_back'>
        Return to metadata
      </Button>
    </div>
  )
}

export default ViewMetadataButton
