import { Sidebar as SidebarComponent } from '@bitcine/cinesend-theme'
import { useNavigate, useLocation } from 'react-router-dom'
import AirlineCycleDropdowns from './airline_cycle_dropdowns'
import useUtilities from '../../hooks/use_utilities'
import { useQuery } from '@tanstack/react-query'
import ViewMetadataButton from './view_metadata_button'

function Sidebar({ user, logOut }) {
  const navigate = useNavigate()
  const { assetTypes } = useUtilities()

  const fullPathname = useLocation().pathname
  const airlineID = fullPathname.match(/airlines\/(\d+)/)?.[1] || null
  const cycleID = fullPathname.match(/cycles\/(\d+)/)?.[1] || null

  const root = `/airlines/${airlineID}/cycles/${cycleID}`
  let pathname = fullPathname.replace(root, '')

  const { data: cycle } = useQuery(['cycles/', cycleID])
  const availableAssetTypes = cycle
    ? assetTypes
        ?.filter((assetType) => cycle?.airline?.asset_types?.includes(assetType.value))
        .map((assetType) => ({
          text: assetType.plural_label,
          to: `/metadata/${assetType.route_value}`,
          route: `${root}/metadata/${assetType.route_value}`,
        }))
    : []

  const viewSettings = pathname.startsWith('/settings')
  if (viewSettings) {
    pathname = pathname.replace('/settings', '')
  }

  const airlineCycleLinks = [
    {
      to: '/dashboard',
      route: `${root}/dashboard`,
      text: 'Dashboard',
      icon: 'dashboard',
    },
    {
      to: '/issues',
      route: `${root}/issues`,
      text: 'Issues',
      icon: 'warning',
    },
    {
      to: '/details',
      route: `${root}/details`,
      text: 'Details',
      icon: 'date_range',
    },
    {
      to: '/images',
      route: `${root}/images`,
      text: 'Bulk Upload Images',
      icon: 'image',
    },
    {
      text: 'Categories',
      to: `/categories`,
      route: `${root}/categories`,
      icon: 'category',
    },
    {
      to: '/metadata',
      text: 'Metadata',
      route: `${root}/metadata`,
      icon: 'list',
      children: availableAssetTypes,
    },
  ]

  const settingsLinks = !user.has_unrestricted_edit_role
    ? []
    : [
        {
          to: '/airlines',
          route: '/settings/airlines',
          text: 'Airlines',
          icon: 'flight',
        },
        {
          to: '/cycles',
          route: '/settings/cycles',
          text: 'Cycles',
          icon: 'date_range',
        },
        {
          to: '/schemas',
          route: '/settings/schemas',
          text: 'Schemas',
          icon: 'list',
        },
        {
          to: '/validators',
          route: '/settings/validators',
          text: 'Validators',
          icon: 'verified_user',
        },
        {
          to: '/users',
          route: '/settings/users',
          text: 'Users',
          icon: 'person',
        },
        {
          to: '/organizations',
          route: '/settings/organizations',
          text: 'Organizations',
          icon: 'apartment',
        },
      ]

  return (
    <div className='flex dark h-full'>
      <SidebarComponent
        header={{
          logo: (
            <img
              src={'https://viasat.brightspotgocdn.com/7a/0b/3b89c915443ab0daf7453aae08b3/white-logo.png'}
              className='w-full'
            />
          ),
        }}
        secondaryHeader={viewSettings ? <ViewMetadataButton /> : <AirlineCycleDropdowns />}
        pathname={pathname}
        small
        lockExpanded={true}
        links={(viewSettings ? settingsLinks : airlineCycleLinks)
          .filter((opt) => !opt.hide)
          .map((opt) => ({
            ...opt,
            onClick: () => navigate(opt.route || opt.to),
            children: opt.children?.map((child) => ({
              ...child,
              onClick: () => navigate(child.route),
            })),
          }))}
        footer={{
          name: user?.name,
          organization: user?.organization?.name,
          dropdownContent: [
            {
              text: 'My Account',
              icon: 'person',
              onClick: () => navigate('/settings/account'),
              show: true,
            },
            {
              text: 'Settings',
              icon: 'settings',
              onClick: () => {
                localStorage.setItem('backToMetadata', fullPathname)
                navigate('/settings')
              },
              show: user.has_unrestricted_edit_role,
            },
            {
              text: 'Log Out',
              icon: 'logout',
              onClick: () => logOut.mutate(),
              show: true,
            },
          ].filter((opt) => opt.show),
        }}
      />
    </div>
  )
}

export default Sidebar
