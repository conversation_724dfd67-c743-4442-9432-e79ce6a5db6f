import { Status } from '@bitcine/cinesend-theme/dist'
import { useQuery } from '@tanstack/react-query'
import { useEffect, useState } from 'react'
import AirlineDropdown from './airline_dropdown'
import CycleDropdown from './cycle_dropdown'
import { useLocation, useNavigate } from 'react-router-dom'

const AirlineCycleDropdowns = ({ redirect = false }) => {
  const pathname = useLocation().pathname

  const requestedAirlineID = pathname.match(/airlines\/(\d+)/)?.[1] || null
  const requestedCycleID = pathname.match(/cycles\/(\d+)/)?.[1] || null

  const navigate = useNavigate()
  const [airlineID, setAirlineID] = useState(parseInt(requestedAirlineID))
  const [cycleID, setCycleID] = useState(parseInt(requestedCycleID))

  const { data: airlines } = useQuery(['airlines'])

  useEffect(() => {
    if (airlines && airlines.length > 0) {
      updateAirlineID(requestedAirlineID ? parseInt(requestedAirlineID) : airlines[0].id)
    }
  }, [requestedAirlineID, airlines])
  const updateAirlineID = (airlineID) => {
    setAirlineID(airlineID)
  }
  const updateCycleID = (cycleID) => {
    setCycleID(cycleID)
    const root = `/airlines/${airlineID}/cycles/${cycleID}`
    const rest = pathname.split(/cycles\/\d+/)?.[1]
    navigate(`${root}${rest || ''}`)
  }

  return (
    <div className='flex items-center justify-center mb-4 border-y border-gray-600 py-4'>
      <Status pending={!airlines} error={!airlines}>
        {airlineID ? (
          <div className='space-y-2 w-full mx-4'>
            <AirlineDropdown airlineID={airlineID} airlines={airlines} setAirlineID={updateAirlineID} />
            <CycleDropdown airlineID={airlineID} cycleID={cycleID} setCycleID={updateCycleID} />
          </div>
        ) : null}
      </Status>
    </div>
  )
}

export default AirlineCycleDropdowns
