import { ButtonDropdown, Icon } from '@bitcine/cinesend-theme/dist'

const AirlineDropdown = ({ airlines, airlineID, setAirlineID }) => {
  const airline = airlines.find((airline) => airline.id === airlineID)
  return (
    <ButtonDropdown
      button={{
        component: (
          <div className='w-56 justify-between text-xs flex items-center rounded bg-primary-700 hover:bg-primary-600 px-4'>
            <Icon icon='flight' className='text-xl text-white' />
            <span className='text-xs text-gray-200'>{airline?.name}</span>
            <Icon icon='arrow_drop_down' className='text-xl text-white' />
          </div>
        ),
      }}
      dropdown={{
        content: airlines?.map((airline) => ({
          icon: airline.id === airlineID ? 'check_circle' : '',
          className: airline.id === airlineID ? 'text-green-600' : 'text-gray-500',
          text: airline.name,
          onClick: () => setAirlineID(airline.id),
        })),
      }}
    />
  )
}

export default AirlineDropdown
