import useUtilities from '../../hooks/use_utilities'
import { ButtonDropdown } from '@bitcine/cinesend-theme/dist'
import useStore from '../../hooks/use_store'
import { useNavigate, useParams } from 'react-router-dom'

const Settings = ({ asset }) => {
  const navigate = useNavigate()
  const { cycleID, airlineID } = useParams()
  const { assetTypes } = useUtilities()
  const { deleteModel } = useStore()
  const assetType = assetTypes?.find((opt) => opt.value === asset?.asset_type)
  return (
    <ButtonDropdown
      button={{
        icon: 'settings',
        minimal: true,
      }}
      dropdown={{
        content: [
          {
            text: 'Delete asset',
            icon: 'delete_forever',
            className: 'text-red-600',
            onClick: () =>
              deleteModel.mutate({
                endpoint: `cycles/${cycleID}/assets`,
                id: asset.id,
                onSuccess: () =>
                  navigate(`/airlines/${airlineID}/cycles/${cycleID}/metadata/${assetType?.route_value}`),
              }),
          },
        ],
      }}
    />
  )
}

export default Settings
