import { Input } from '@bitcine/cinesend-theme/dist'
import { useState } from 'react'
import useAuth from '../../hooks/use_auth'

const AssetIdentifier = ({ asset, saveAsset }) => {
  const [title, setTitle] = useState(asset.title)
  const { user } = useAuth()

  return (
    <Input
      label={'Asset Identifier'}
      value={title}
      onChange={(e) => setTitle(e.target.value)}
      onBlur={() => saveAsset({ title })}
      disabled={!user.has_unrestricted_edit_role}
    />
  )
}

export default AssetIdentifier
