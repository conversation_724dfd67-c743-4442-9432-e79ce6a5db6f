import { useQuery } from '@tanstack/react-query'
import Tabs from '../layouts/tabs'
import { Navigate, Route, Routes, useLocation } from 'react-router-dom'
import FieldsByLanguage from './fields_by_language'

const LocalizableFields = ({ assetID, airlineID, cycleID }) => {
  const { data: airline } = useQuery([`airlines/${airlineID}`])
  const languageCode = useLocation().pathname.split('/').pop()

  if (!airline) {
    return null
  }

  return (
    <div className='space-y-4 mt-4'>
      <Tabs
        list={airline?.languages?.map((language) => ({ name: language.eng_description, to: language.ife_code })) || []}
      />
      <Routes key={languageCode}>
        {airline?.languages?.map((language) => (
          <Route
            path={`/${language.ife_code}`}
            key={language.ife_code}
            element={<FieldsByLanguage cycleID={cycleID} languageID={language.id} assetID={assetID} />}
          />
        ))}
        <Route path='*' element={<Navigate to={airline.languages[0].ife_code} replace={true} />} />
      </Routes>
    </div>
  )
}

export default LocalizableFields
