import { useQuery } from '@tanstack/react-query'
import FieldValue from '../field_value'
import { Status } from '@bitcine/cinesend-theme/dist'

const FieldsByLanguage = ({ assetID, languageID, cycleID }) => {
  const {
    data: fields,
    isLoading,
    isFetching,
    isError,
    error,
  } = useQuery([`field-values`, `?assetID=${assetID}`, `&languageID=${languageID}`, `&cycleID=${cycleID}`])

  return (
    <Status pending={isLoading || isFetching} isError={isError} errorMessage={error?.message}>
      <div className='space-y-4'>
        {fields?.map((field, index) => (
          <FieldValue
            key={index}
            field={field}
            isLoading={isLoading || isFetching}
            assetID={assetID}
            cycleID={cycleID}
            languageID={languageID}
          />
        ))}
      </div>
    </Status>
  )
}

export default FieldsByLanguage
