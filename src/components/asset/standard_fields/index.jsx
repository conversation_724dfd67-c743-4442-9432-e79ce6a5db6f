import { useQuery, useQueryClient } from '@tanstack/react-query'
import { Message, Poster, Status } from '@bitcine/cinesend-theme/dist'
import useStore from '../../../hooks/use_store'
import AssetIdentifier from '../asset_identifier'
import Date from '../date'
import FieldValue from '../../field_value'
import Languages from './languages'
import { useNavigate, useParams } from 'react-router-dom'

const LENGTH = 'Length'
const POSTER = 'Poster'
const RELEASE_YEAR = 'Year of Release'
const FEATURE_FILE_NAME = 'Feature File Name (.mp4)'

const StandardFields = ({ asset, cycleID }) => {
  const params = useParams()
  const navigate = useNavigate()
  const queryClient = useQueryClient()
  const {
    data: fields,
    isLoading,
    isError,
    error,
  } = useQuery([`field-values`, `?assetID=${asset.id}`, `&cycleID=${cycleID}`, '&isStandard=true'])
  const { updateModel } = useStore()
  const saveAsset = (data) =>
    updateModel.mutate(
      {
        endpoint: `cycles/${cycleID}/assets`,
        id: asset.id,
        data,
      },
      {
        onSuccess: (res) => {
          if (res.data.asset.id !== asset.id) {
            navigate(
              `/airlines/${params.airlineID}/cycles/${cycleID}/metadata/${params.assetType}/${res.data.asset.id}/details/${params['*']}`
            )
          }
        },
      }
    )

  const posterField = fields?.find((field) => field.name === POSTER)
  const lengthField = fields?.find((field) => field.name === LENGTH)
  const releaseYearField = fields?.find((field) => field.name === RELEASE_YEAR)
  const featureFileName = fields?.find((field) => field.name === FEATURE_FILE_NAME)
  const assetDateIssue = asset?.asset_issues?.find((issue) => issue.validator_group === 'date_order')

  const refreshValues = () => {
    queryClient.resetQueries([`field-values`, `?assetID=${asset.id}`, `&cycleID=${cycleID}`, `&isStandard=true`])
  }

  return (
    <Status pending={isLoading} isError={isError} errorMessage={error?.message}>
      <div className='flex space-x-4'>
        {posterField ? (
          !posterField.asset_image?.url ? (
            <FieldValue className='aspect-[2/3] max-w-60' field={posterField} assetID={asset.id} cycleID={cycleID} />
          ) : (
            <div className='aspect-[2/3] max-w-60'>
              <Poster url={posterField.asset_image?.url} />
            </div>
          )
        ) : null}
        <div className='w-full space-y-4'>
          <div className='flex space-x-4'>
            <AssetIdentifier asset={asset} saveAsset={saveAsset} />
          </div>
          <div className='w-full flex space-x-4'>
            {lengthField ? <FieldValue cycleID={cycleID} field={lengthField} assetID={asset.id} /> : null}
            {releaseYearField ? <FieldValue cycleID={cycleID} field={releaseYearField} assetID={asset.id} /> : null}
            <Date asset={asset} type={'Start'} saveAsset={saveAsset} />
            <Date asset={asset} type={'End'} saveAsset={saveAsset} />
          </div>
          {assetDateIssue && (
            <div className='w-full'>
              <Message error={true} small={true}>
                {assetDateIssue.description}
              </Message>
            </div>
          )}
          <Languages cycleID={cycleID} asset={asset} fields={fields} />
          {featureFileName ? (
            <FieldValue cycleID={cycleID} field={featureFileName} assetID={asset.id} refreshValues={refreshValues} />
          ) : null}
        </div>
      </div>
    </Status>
  )
}

export default StandardFields
