import FieldValue from '../../field_value'

const AUDIO_TRACKS = ['Lang 1', 'Lang 2', 'Lang 3', 'Lang 4', 'Lang 5', 'Lang 6']

const TEXT_TRACKS = ['Sub Embed 1', 'Sub Embed 2', 'Sub Dynamic 1', 'Sub Dynamic 2', 'Closed Caps']

const Languages = ({ asset, fields, cycleID }) => {
  return (
    <div className='space-y-4'>
      <div className='flex space-x-4'>
        {fields
          .filter((field) => AUDIO_TRACKS.includes(field.name))
          .map((field) => (
            <FieldValue cycleID={cycleID} key={field.id} field={field} assetID={asset.id} />
          ))}
      </div>
      <div className='flex space-x-4'>
        {fields
          .filter((field) => TEXT_TRACKS.includes(field.name))
          .map((field) => (
            <FieldValue cycleID={cycleID} key={field.id} field={field} assetID={asset.id} />
          ))}
      </div>
    </div>
  )
}

export default Languages
