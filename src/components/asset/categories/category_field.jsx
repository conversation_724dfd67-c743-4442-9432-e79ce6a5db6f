import { useEffect, useState } from 'react'
import { Select } from '@bitcine/cinesend-theme/dist'
import useStore from '../../../hooks/use_store'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import axios from 'axios'

const CategoryField = ({ title, options, value, assetID, cycleID, langID }) => {
  const [currentValues, setCurrentValues] = useState([])
  const [category, setCategory] = useState()
  const { createModel } = useStore()
  const queryClient = useQueryClient()

  useEffect(() => {
    setCurrentValues(value)
  }, [value])

  const updateAssetCategory = (newValues) => {
    if (currentValues.length < newValues.length) {
      // get difference and add
      const category = newValues.filter((item) => !currentValues.includes(item))[0]
      addCategory(category)
    } else {
      // get difference and remove
      const category = currentValues.filter((item) => !newValues.includes(item))[0]
      setCategory(category)
      removeCategory.mutate()
    }
  }

  const addCategory = (category) => {
    createModel.mutate(
      {
        endpoint: `categories/${category.value}/add-items`,
        data: {
          type: 'assets',
          ids: [assetID],
          cycleID: cycleID,
        },
      },
      {
        onSuccess: () => {
          setCurrentValues([...currentValues, category])
          queryClient.invalidateQueries([`cycles/${cycleID}/assets/${assetID}`])
        },
        onError: (err) => console.error(err),
      }
    )
  }

  const removeCategory = useMutation(
    () =>
      axios.delete(`/categories/${category.value}/remove-items`, {
        data: { ids: [assetID], cycleID: cycleID, type: 'assets' },
      }),
    {
      onSuccess: () => {
        setCurrentValues(currentValues.filter((item) => item !== category))
        queryClient.invalidateQueries([`cycles/${cycleID}/assets/${assetID}`])
      },
      onError: (err) => console.error(err),
    }
  )

  const getTitle = (category) => {
    return category.category_values?.find((v) => v.language_id === langID)?.value
  }

  return (
    <Select
      label={title}
      options={options.map((item) => ({ value: item.id, label: getTitle(item) }))}
      value={currentValues}
      isMulti={true}
      onChange={(value) => updateAssetCategory(value)}
      className={'pt-2'}
    />
  )
}

export default CategoryField
