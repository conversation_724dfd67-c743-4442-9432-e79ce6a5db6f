import { useQuery } from '@tanstack/react-query'
import { useParams, Routes, Route, Navigate } from 'react-router-dom'
import { Status } from '@bitcine/cinesend-theme'
import useUtilities from '../../hooks/use_utilities'
import AssetsCollectionTable from '../assets_table/sub_assets_table'
import AllFields from './all_fields'
import Page from '../layouts/page'
import CompletionLabel from '../completion_label'
import Settings from './settings'

const Asset = ({ cycle }) => {
  const { assetID, parentAssetID } = useParams()
  const { data: asset, isLoading, isError, error } = useQuery([`cycles/${cycle.id}/assets/${assetID}`])
  const { assetTypes } = useUtilities()
  const childAssetTypes = assetTypes.filter((type) => type.parent_type === asset?.asset_type)
  const parentAssetType = assetTypes.find((type) => type.value === asset?.parent_asset_type)
  const breadcrumb =
    parentAssetType && parentAssetID
      ? [
          {
            text: parentAssetType?.plural_label,
            to: `/airlines/${asset?.cycle?.airline_id}/cycles/${asset?.cycle.id}/metadata/${parentAssetType?.route_value}`,
          },
          {
            text: asset?.parent_asset?.title,
            to: `/airlines/${asset?.cycle?.airline_id}/cycles/${asset?.cycle.id}/metadata/${parentAssetType?.route_value}/${parentAssetID}`,
          },
          {
            text: asset?.plural_label,
            to: `/airlines/${asset?.cycle?.airline_id}/cycles/${asset?.cycle.id}/metadata/${parentAssetType?.route_value}/${parentAssetID}/${asset?.route_value}`,
          },
        ]
      : [
          {
            text: asset?.plural_label,
            to: `/airlines/${asset?.cycle?.airline_id}/cycles/${asset?.cycle.id}/metadata/${asset?.route_value}`,
          },
        ]
  return (
    <Page
      breadcrumbs={breadcrumb.concat([
        {
          text: asset?.title,
        },
      ])}
      tabs={[{ name: 'Metadata', to: 'details' }]
        .concat(
          childAssetTypes.map((assetType) => ({
            name: assetType.plural_label,
            to: `${assetType.route_value}`,
          }))
        )
        .filter((e, i, arr) => arr.length > 1)} // no need for tabs when there's no childs
      buttons={
        <div className='flex space-x-2 items-center w-80'>
          <CompletionLabel percentage={asset?.completion_percentage} />
          <Settings asset={asset} />
        </div>
      }
    >
      <Status pending={isLoading} error={isError} errorMessage={error?.message}>
        <Routes>
          {childAssetTypes?.map((assetType) => (
            <Route
              key={assetType.value}
              path={`/${assetType.route_value}`}
              element={
                <AssetsCollectionTable
                  cycle={cycle}
                  assetId={assetID}
                  assetType={assetType}
                  childAssets={asset?.child_assets}
                />
              }
            />
          ))}
          <Route
            path='/details/*'
            element={asset ? <AllFields asset={asset} airlineID={cycle?.airline_id} cycleID={cycle.id} /> : null}
          />
          <Route path='*' element={<Navigate to={`details`} replace={true} />} />
        </Routes>
      </Status>
    </Page>
  )
}

export default Asset
