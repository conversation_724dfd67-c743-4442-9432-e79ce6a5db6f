import { useQuery, useQueryClient } from '@tanstack/react-query'
import FieldValue from '../field_value'
import { Status } from '@bitcine/cinesend-theme/dist'

const CommonFields = ({ assetID, cycleID }) => {
  const queryClient = useQueryClient()
  const queryKey = [`field-values?assetID=${assetID}&cycleID=${cycleID}`]
  const { data: fields, isLoading, isError, error } = useQuery(queryKey)
  const refreshValues = () => queryClient.resetQueries(queryKey)

  return (
    <Status pending={isLoading} isError={isError} errorMessage={error?.message}>
      <div className='space-y-4 mt-4'>
        {fields?.map((opt, index) => (
          <div key={index}>
            <FieldValue field={opt} assetID={assetID} cycleID={cycleID} refreshValues={refreshValues} />
          </div>
        ))}
      </div>
    </Status>
  )
}

export default CommonFields
