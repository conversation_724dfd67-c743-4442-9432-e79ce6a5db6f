import { useContext } from 'react'
import { StoreContext } from '../contexts/store'

export const useSnackbar = () => {
  const { dispatch } = useContext(StoreContext)

  const addMessage = (text, type = 'success', icon = 'check_circle', timeout = 4000, showCloseButton = false) => {
    const _id = Math.random().toString(36).substring(7)
    const message = {
      _id,
      message: text,
      icon,
      type,
      timeout,
      showCloseButton,
      onClose: () => removeMessage(_id),
    }
    dispatch({ type: 'add_message', message })
  }

  const addErrorMessage = (text) => {
    addMessage(text, 'error', 'error', false, true)
  }

  const removeMessage = (messageID) => {
    dispatch({ type: 'remove_message', messageID })
  }

  return {
    addMessage,
    addErrorMessage,
  }
}
