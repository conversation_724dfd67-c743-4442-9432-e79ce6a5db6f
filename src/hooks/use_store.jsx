import axios from 'axios'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { useSnackbar } from './use_snackbar'

export default function useStore() {
  const queryClient = useQueryClient()
  const { addMessage } = useSnackbar()

  function handleSuccess(res, opt) {
    queryClient.invalidateQueries([opt.endpoint])
    queryClient.invalidateQueries([opt.endpoint + '/'])
    if (opt.id) {
      queryClient.invalidateQueries([opt.endpoint + '/' + opt.id])
    }
    if (res.data.message && !opt.silent) {
      if (res.data.alert) {
        addMessage(res.data.message, 'warning', 'check_circle', null)
      } else {
        addMessage(res.data.message)
      }
    }
    if (opt.onSuccess) {
      opt.onSuccess(res, opt)
    }
  }

  function handleError(res, opt) {
    if (res.response.data.message) {
      addMessage(res.response.data.message, 'error')
    }
    if (opt.onError) {
      opt.onError(res)
    }
  }

  const createModel = useMutation(
    (opt) => {
      return axios.post(opt.endpoint, opt.data)
    },
    {
      onSuccess: handleSuccess,
      onError: handleError,
    }
  )

  const updateModel = useMutation(
    (opt) => {
      return axios.put(`${opt.endpoint}/${opt.id}`, {
        ...opt.data,
      })
    },
    {
      onSuccess: handleSuccess,
      onError: handleError,
    }
  )

  const updateModels = useMutation(
    (opt) => {
      return axios.put(`${opt.endpoint}/mass-update`, {
        ids: opt.ids,
        data: opt.data,
      })
    },
    {
      onSuccess: handleSuccess,
    }
  )

  const deleteModel = useMutation(
    (opt) => {
      if (window.confirm(opt.confirmMessage ?? 'Are you sure you want to delete this?')) {
        return axios.delete(`${opt.endpoint}/${opt.id}`)
      }
    },
    {
      onSuccess: handleSuccess,
    }
  )

  const deleteModels = useMutation(
    (opt) => {
      return axios.post(`${opt.endpoint}/mass-delete`, {
        ids: opt.ids,
      })
    },
    {
      onSuccess: handleSuccess,
    }
  )

  return {
    createModel,
    updateModel,
    updateModels,
    deleteModel,
    deleteModels,
  }
}
