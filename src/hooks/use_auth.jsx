import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import axios from 'axios'
import { modelTypes } from '../constants/model_types'

export default function useAuth(modelType = null, model = null) {
  const queryClient = useQueryClient()

  const { data, error, isLoading, isFetching, isRefetching } = useQuery(['/user'], {
    retry: false,
    refetchOnWindowFocus: true,
  })

  // Only set the user if there is no error.
  const user = !error ? data : null

  const logOut = useMutation(
    () => {
      return axios.post('/token-sign-out')
    },
    {
      onSuccess: () => {
        localStorage.removeItem('user_token')
        queryClient.invalidateQueries(['/user'])
        window.location.reload()
      },
    }
  )

  const canEdit = () => {
    if (modelType === null) {
      return false
    }

    switch (modelType) {
      case modelTypes.Category:
        if (model === null) {
          return user.has_unrestricted_edit_role
        }

        return (user.has_unrestricted_edit_role && model.is_root) || !model.is_root
      case modelTypes.Cycle:
        return user.has_unrestricted_edit_role
      default:
        return true
    }
  }

  return {
    user,
    isLoading,
    isFetching,
    isRefetching,
    logOut,
    canEditModel: canEdit(),
  }
}
