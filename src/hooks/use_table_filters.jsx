import { useState, useMemo, useEffect } from 'react'

const toQueryString = (obj, prefix) => {
  return Object.entries(obj).flatMap(([key, value]) => {
    const fullKey = prefix ? `${prefix}[${key}]` : key
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      return toQueryString(value, fullKey)
    } else {
      return `${encodeURIComponent(fullKey)}=${encodeURIComponent(value ?? '')}`
    }
  })
}

const useTableFilters = ({
  sortingOptions = null,
  filteringOptions = null,
  enableSearch = true,
  initialSort = { key: 'created_at', direction: 'desc' },
  initialPagination = { currentPage: 0, rowsPerPage: 10, totalRows: 0 },
  queryExists = false, // if url query string (?...) already present
}) => {
  useEffect(() => {
    if (initialPagination.totalRows !== pagination.totalRows) {
      setFilters((prev) => ({
        ...prev,
        pagination: { ...initialPagination },
      }))
    }
  }, [initialPagination])

  const [filters, setFilters] = useState({
    search: '',
    filter: {},
    pagination: initialPagination,
    sorting: initialSort,
  })

  const queryString = useMemo(() => {
    const params = toQueryString(filters)
    const prefix = queryExists ? '&' : '?'
    return params.length ? `${prefix}${params.join('&')}` : ''
  }, [filters])

  const searching = enableSearch
    ? {
        search: filters.search,
        searchPlaceholder: 'Search...',
        onSearch: (search) =>
          setFilters((prev) => ({ ...prev, search, pagination: { ...prev.pagination, currentPage: 0 } })),
        rightIcon: {
          icon: 'close',
          onClick: () => setFilters((prev) => ({ ...prev, search: '' })),
          tooltip: { text: 'Clear search' },
        },
      }
    : null

  const sorting = sortingOptions
    ? {
        options: sortingOptions,
        key: filters.sorting.key,
        sortingKey: filters.sorting.key,
        direction: filters.sorting.direction,
        onSortChange: (sort) =>
          setFilters((prev) => ({
            ...prev,
            sorting: { key: sort.key, direction: sort.direction },
          })),
      }
    : null

  const filtering = filteringOptions
    ? {
        options: filteringOptions,
        filters: filters.filter,
        onFiltersChange: (value) =>
          setFilters((prev) => ({ ...prev, filter: value, pagination: { ...prev.pagination, currentPage: 0 } })),
      }
    : null

  const pagination = {
    currentPage: filters.pagination.currentPage,
    rowsPerPage: filters.pagination.rowsPerPage,
    totalRows: filters.pagination.totalRows,
    onPageChange: (currentPage) =>
      setFilters((prev) => ({
        ...prev,
        pagination: { ...prev.pagination, currentPage },
      })),
    onRowsPerPageChange: (rowsPerPage) =>
      setFilters((prev) => ({
        ...prev,
        pagination: { ...prev.pagination, rowsPerPage },
      })),
  }

  return {
    filters,
    queryString,
    searching,
    sorting,
    filtering,
    pagination,
  }
}

export default useTableFilters
